package com.fartech.aiagent.services;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * HTTP client service for making API requests to AI providers.
 * This implementation uses Java's built-in HttpURLConnection for Java 8 compatibility.
 */
public class HttpClientService {
    
    private static final int DEFAULT_TIMEOUT = 30000; // 30 seconds
    private static final int DEFAULT_READ_TIMEOUT = 60000; // 60 seconds
    
    private int connectTimeout = DEFAULT_TIMEOUT;
    private int readTimeout = DEFAULT_READ_TIMEOUT;
    
    /**
     * Sets the connection timeout.
     * @param timeoutMs timeout in milliseconds
     */
    public void setConnectTimeout(int timeoutMs) {
        this.connectTimeout = timeoutMs;
    }
    
    /**
     * Sets the read timeout.
     * @param timeoutMs timeout in milliseconds
     */
    public void setReadTimeout(int timeoutMs) {
        this.readTimeout = timeoutMs;
    }
    
    /**
     * Makes a GET request to the specified URL.
     * @param url the URL to request
     * @param headers optional headers to include
     * @return the response body as a string
     * @throws IOException if the request fails
     */
    public String get(String url, Map<String, String> headers) throws IOException {
        HttpURLConnection connection = createConnection(url, "GET", headers);
        return readResponse(connection);
    }
    
    /**
     * Makes a POST request to the specified URL with JSON payload.
     * @param url the URL to request
     * @param payload the request payload (will be converted to JSON)
     * @param headers optional headers to include
     * @return the response body as a string
     * @throws IOException if the request fails
     */
    public String post(String url, Object payload, Map<String, String> headers) throws IOException {
        HttpURLConnection connection = createConnection(url, "POST", headers);
        
        // Convert payload to JSON and write to request body
        String jsonPayload = toJson(payload);
        connection.setDoOutput(true);
        
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        return readResponse(connection);
    }
    
    /**
     * Makes a PUT request to the specified URL with JSON payload.
     * @param url the URL to request
     * @param payload the request payload (will be converted to JSON)
     * @param headers optional headers to include
     * @return the response body as a string
     * @throws IOException if the request fails
     */
    public String put(String url, Object payload, Map<String, String> headers) throws IOException {
        HttpURLConnection connection = createConnection(url, "PUT", headers);
        
        // Convert payload to JSON and write to request body
        String jsonPayload = toJson(payload);
        connection.setDoOutput(true);
        
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        return readResponse(connection);
    }
    
    /**
     * Makes a DELETE request to the specified URL.
     * @param url the URL to request
     * @param headers optional headers to include
     * @return the response body as a string
     * @throws IOException if the request fails
     */
    public String delete(String url, Map<String, String> headers) throws IOException {
        HttpURLConnection connection = createConnection(url, "DELETE", headers);
        return readResponse(connection);
    }
    
    /**
     * Creates and configures an HTTP connection.
     * @param url the URL to connect to
     * @param method the HTTP method
     * @param headers optional headers to include
     * @return the configured connection
     * @throws IOException if the connection cannot be created
     */
    private HttpURLConnection createConnection(String url, String method, Map<String, String> headers) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        
        // Set timeouts
        connection.setConnectTimeout(connectTimeout);
        connection.setReadTimeout(readTimeout);
        
        // Set method
        connection.setRequestMethod(method);
        
        // Set default headers
        connection.setRequestProperty("User-Agent", "Eclipse-AI-Agent/1.0");
        connection.setRequestProperty("Accept", "application/json");
        
        // Set custom headers
        if (headers != null) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
        }
        
        return connection;
    }
    
    /**
     * Reads the response from an HTTP connection.
     * @param connection the HTTP connection
     * @return the response body as a string
     * @throws IOException if reading the response fails
     */
    private String readResponse(HttpURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        
        // Determine which stream to read from
        BufferedReader reader;
        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
        }
        
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line).append('\n');
        }
        reader.close();
        
        // Check for HTTP errors
        if (responseCode >= 400) {
            throw new IOException("HTTP " + responseCode + ": " + response.toString());
        }
        
        return response.toString();
    }
    
    /**
     * Converts an object to JSON string.
     * This is a simplified JSON serialization for basic objects.
     * In a real implementation, you would use a proper JSON library like Jackson or Gson.
     * @param obj the object to convert
     * @return the JSON string
     */
    @SuppressWarnings("unchecked")
    private String toJson(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        if (obj instanceof String) {
            return "\"" + escapeJson((String) obj) + "\"";
        }
        
        if (obj instanceof Number || obj instanceof Boolean) {
            return obj.toString();
        }
        
        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;
            StringBuilder sb = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (!first) {
                    sb.append(",");
                }
                sb.append("\"").append(escapeJson(entry.getKey())).append("\":");
                sb.append(toJson(entry.getValue()));
                first = false;
            }
            sb.append("}");
            return sb.toString();
        }
        
        if (obj instanceof Object[]) {
            Object[] array = (Object[]) obj;
            StringBuilder sb = new StringBuilder("[");
            for (int i = 0; i < array.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append(toJson(array[i]));
            }
            sb.append("]");
            return sb.toString();
        }
        
        // Fallback for other objects
        return "\"" + escapeJson(obj.toString()) + "\"";
    }
    
    /**
     * Escapes special characters in JSON strings.
     * @param str the string to escape
     * @return the escaped string
     */
    private String escapeJson(String str) {
        if (str == null) {
            return "";
        }
        
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\b", "\\b")
                  .replace("\f", "\\f")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
