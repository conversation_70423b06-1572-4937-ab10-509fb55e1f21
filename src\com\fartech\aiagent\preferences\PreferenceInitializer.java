package com.fartech.aiagent.preferences;

import org.eclipse.core.runtime.preferences.AbstractPreferenceInitializer;
import org.eclipse.jface.preference.IPreferenceStore;

/**
 * Initializes default preferences for the AI Agent plugin.
 */
public class PreferenceInitializer extends AbstractPreferenceInitializer {
    
    @Override
    public void initializeDefaultPreferences() {
        IPreferenceStore store = AIAgentPreferences.getPreferenceStore();
        
        // Set default provider to OpenAI
        store.setDefault("selected_provider", "openai");
        
        // Set default OpenAI configuration
        store.setDefault("provider_config_openai_base_url", "https://api.openai.com/v1");
        store.setDefault("provider_config_openai_model", "gpt-3.5-turbo");
        
        // Set default Anthropic configuration
        store.setDefault("provider_config_anthropic_base_url", "https://api.anthropic.com");
        store.setDefault("provider_config_anthropic_model", "claude-3-sonnet-20240229");
        store.setDefault("provider_config_anthropic_version", "2023-06-01");

        // Set default Custom provider configuration
        store.setDefault("provider_config_custom_api_format", "openai");
        store.setDefault("provider_config_custom_auth_header", "Authorization");
        store.setDefault("provider_config_custom_endpoint_path", "/v1/chat/completions");
        store.setDefault("provider_config_custom_available_models", "gpt-3.5-turbo,gpt-4,custom-model-1,custom-model-2");
    }
}
