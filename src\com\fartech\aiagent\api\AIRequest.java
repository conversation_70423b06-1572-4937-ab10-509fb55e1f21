package com.fartech.aiagent.api;

import java.util.Map;
import java.util.HashMap;

/**
 * Represents a request to an AI provider.
 */
public class AIRequest {
    
    private String prompt;
    private String systemMessage;
    private double temperature = 0.7;
    private int maxTokens = 1000;
    private String model;
    private Map<String, Object> additionalParameters;
    
    public AIRequest(String prompt) {
        this.prompt = prompt;
        this.additionalParameters = new HashMap<>();
    }
    
    public AIRequest(String prompt, String systemMessage) {
        this.prompt = prompt;
        this.systemMessage = systemMessage;
        this.additionalParameters = new HashMap<>();
    }
    
    // Getters and setters
    public String getPrompt() {
        return prompt;
    }
    
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    
    public String getSystemMessage() {
        return systemMessage;
    }
    
    public void setSystemMessage(String systemMessage) {
        this.systemMessage = systemMessage;
    }
    
    public double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }
    
    public int getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(int maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public Map<String, Object> getAdditionalParameters() {
        return additionalParameters;
    }
    
    public void setAdditionalParameter(String key, Object value) {
        this.additionalParameters.put(key, value);
    }
    
    public Object getAdditionalParameter(String key) {
        return this.additionalParameters.get(key);
    }
}
