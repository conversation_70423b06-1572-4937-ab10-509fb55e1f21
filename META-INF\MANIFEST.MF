Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: AI Coding Agent
Bundle-SymbolicName: com.fartech.aiagent;singleton:=true
Bundle-Version: 1.0.0.qualifier
Bundle-Activator: com.fartech.aiagent.Activator
Bundle-Vendor: FarTech
Require-Bundle: org.eclipse.ui,
 org.eclipse.core.runtime,
 org.eclipse.core.resources,
 org.eclipse.ui.editors,
 org.eclipse.ui.workbench.texteditor,
 org.eclipse.jface.text,
 org.eclipse.ui.ide,
 org.eclipse.jdt.core,
 org.eclipse.jdt.ui
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Automatic-Module-Name: com.fartech.aiagent
Bundle-ActivationPolicy: lazy
Export-Package: com.fartech.aiagent.api,
 com.fartech.aiagent.providers,
 com.fartech.aiagent.services
