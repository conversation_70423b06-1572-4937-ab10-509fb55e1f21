package com.fartech.aiagent.services;

import java.util.Map;

import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.jdt.core.ICompilationUnit;
import org.eclipse.jdt.core.IJavaElement;
import org.eclipse.jdt.core.IMethod;
import org.eclipse.jdt.core.IType;

import com.fartech.aiagent.api.AIProvider;
import com.fartech.aiagent.api.AIRequest;
import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.api.AIProviderException;
import com.fartech.aiagent.preferences.AIAgentPreferences;
import com.fartech.aiagent.providers.AIProviderRegistry;

/**
 * Core service for AI Agent functionality.
 * Handles code analysis, generation, and other AI-powered features.
 */
public class AIAgentService {
    
    private static AIAgentService instance;
    private AIProviderRegistry providerRegistry;
    private AIProvider currentProvider;
    
    private AIAgentService() {
        this.providerRegistry = AIProviderRegistry.getInstance();
        initializeProvider();
    }
    
    public static synchronized AIAgentService getInstance() {
        if (instance == null) {
            instance = new AIAgentService();
        }
        return instance;
    }
    
    /**
     * Initializes the AI provider based on current preferences.
     */
    private void initializeProvider() {
        try {
            String providerId = AIAgentPreferences.getSelectedProvider();
            if (providerId != null) {
                AIProvider provider = providerRegistry.createProviderInstance(providerId);
                if (provider != null) {
                    Map<String, String> config = AIAgentPreferences.getProviderConfiguration(providerId);
                    provider.configure(config);
                    this.currentProvider = provider;
                }
            }
        } catch (Exception e) {
            // Log error but don't fail initialization
            System.err.println("Failed to initialize AI provider: " + e.getMessage());
        }
    }
    
    /**
     * Refreshes the AI provider configuration.
     * Call this after preferences have been changed.
     */
    public void refreshProvider() {
        initializeProvider();
    }
    
    /**
     * Checks if the AI agent is ready to use.
     * @return true if a provider is configured and ready
     */
    public boolean isReady() {
        return currentProvider != null && currentProvider.isConfigured();
    }
    
    /**
     * Gets the current AI provider.
     * @return the current provider, or null if none configured
     */
    public AIProvider getCurrentProvider() {
        return currentProvider;
    }
    
    /**
     * Analyzes the given code and provides suggestions.
     * @param code the code to analyze
     * @param context additional context about the code
     * @return AI response with analysis and suggestions
     * @throws AIProviderException if the analysis fails
     */
    public AIResponse analyzeCode(String code, String context) throws AIProviderException {
        if (!isReady()) {
            throw new AIProviderException("AI Agent is not configured. Please configure an AI provider in preferences.");
        }
        
        String prompt = buildAnalysisPrompt(code, context);
        AIRequest request = new AIRequest(prompt);
        request.setSystemMessage("You are an expert Java developer and code reviewer. Analyze the provided code and give constructive feedback, suggestions for improvement, and identify potential issues.");
        request.setMaxTokens(1500);
        request.setTemperature(0.3);
        
        return currentProvider.sendRequest(request);
    }
    
    /**
     * Generates code based on the given description.
     * @param description description of what code to generate
     * @param context additional context (e.g., existing class, method signatures)
     * @return AI response with generated code
     * @throws AIProviderException if code generation fails
     */
    public AIResponse generateCode(String description, String context) throws AIProviderException {
        if (!isReady()) {
            throw new AIProviderException("AI Agent is not configured. Please configure an AI provider in preferences.");
        }
        
        String prompt = buildGenerationPrompt(description, context);
        AIRequest request = new AIRequest(prompt);
        request.setSystemMessage("You are an expert Java developer. Generate clean, well-documented Java code based on the user's requirements. Follow Java best practices and coding conventions.");
        request.setMaxTokens(2000);
        request.setTemperature(0.2);
        
        return currentProvider.sendRequest(request);
    }
    
    /**
     * Explains the given code in natural language.
     * @param code the code to explain
     * @return AI response with code explanation
     * @throws AIProviderException if explanation fails
     */
    public AIResponse explainCode(String code) throws AIProviderException {
        if (!isReady()) {
            throw new AIProviderException("AI Agent is not configured. Please configure an AI provider in preferences.");
        }
        
        String prompt = "Please explain what this Java code does:\n\n```java\n" + code + "\n```\n\nProvide a clear, concise explanation that covers the main functionality, key components, and any notable patterns or techniques used.";
        
        AIRequest request = new AIRequest(prompt);
        request.setSystemMessage("You are an expert Java developer and teacher. Explain code in a clear, educational manner that would be helpful to other developers.");
        request.setMaxTokens(1000);
        request.setTemperature(0.3);
        
        return currentProvider.sendRequest(request);
    }
    
    /**
     * Suggests improvements for the given code.
     * @param code the code to improve
     * @return AI response with improvement suggestions
     * @throws AIProviderException if suggestion fails
     */
    public AIResponse suggestImprovements(String code) throws AIProviderException {
        if (!isReady()) {
            throw new AIProviderException("AI Agent is not configured. Please configure an AI provider in preferences.");
        }
        
        String prompt = "Please review this Java code and suggest improvements:\n\n```java\n" + code + "\n```\n\nFocus on:\n- Code quality and readability\n- Performance optimizations\n- Best practices\n- Potential bugs or issues\n- Design patterns that could be applied";
        
        AIRequest request = new AIRequest(prompt);
        request.setSystemMessage("You are an expert Java developer and code reviewer. Provide constructive feedback and actionable improvement suggestions.");
        request.setMaxTokens(1500);
        request.setTemperature(0.3);
        
        return currentProvider.sendRequest(request);
    }
    
    /**
     * Generates unit tests for the given code.
     * @param code the code to test
     * @param testFramework the testing framework to use (e.g., "JUnit 4", "JUnit 5")
     * @return AI response with generated unit tests
     * @throws AIProviderException if test generation fails
     */
    public AIResponse generateTests(String code, String testFramework) throws AIProviderException {
        if (!isReady()) {
            throw new AIProviderException("AI Agent is not configured. Please configure an AI provider in preferences.");
        }
        
        String framework = testFramework != null ? testFramework : "JUnit 5";
        String prompt = "Generate comprehensive unit tests for this Java code using " + framework + ":\n\n```java\n" + code + "\n```\n\nInclude:\n- Test cases for normal scenarios\n- Edge cases and boundary conditions\n- Error conditions and exception handling\n- Proper test method naming and documentation";
        
        AIRequest request = new AIRequest(prompt);
        request.setSystemMessage("You are an expert Java developer specializing in test-driven development. Generate thorough, well-structured unit tests.");
        request.setMaxTokens(2000);
        request.setTemperature(0.2);
        
        return currentProvider.sendRequest(request);
    }
    
    /**
     * Analyzes a Java compilation unit and provides insights.
     * @param compilationUnit the compilation unit to analyze
     * @return AI response with analysis
     * @throws AIProviderException if analysis fails
     */
    public AIResponse analyzeCompilationUnit(ICompilationUnit compilationUnit) throws AIProviderException {
        try {
            String source = compilationUnit.getSource();
            String className = compilationUnit.getElementName();
            String context = "This is a Java class file: " + className;
            
            return analyzeCode(source, context);
        } catch (Exception e) {
            throw new AIProviderException("Failed to analyze compilation unit", e);
        }
    }
    
    /**
     * Generates documentation for a Java method.
     * @param method the method to document
     * @return AI response with generated documentation
     * @throws AIProviderException if documentation generation fails
     */
    public AIResponse generateMethodDocumentation(IMethod method) throws AIProviderException {
        try {
            String methodSource = method.getSource();
            String prompt = "Generate comprehensive Javadoc documentation for this method:\n\n```java\n" + methodSource + "\n```\n\nInclude:\n- Clear description of what the method does\n- @param tags for all parameters\n- @return tag if applicable\n- @throws tags for exceptions\n- Usage examples if helpful";
            
            AIRequest request = new AIRequest(prompt);
            request.setSystemMessage("You are an expert Java developer. Generate clear, comprehensive Javadoc documentation following standard conventions.");
            request.setMaxTokens(800);
            request.setTemperature(0.2);
            
            return currentProvider.sendRequest(request);
        } catch (Exception e) {
            throw new AIProviderException("Failed to generate method documentation", e);
        }
    }
    
    /**
     * Builds a prompt for code analysis.
     * @param code the code to analyze
     * @param context additional context
     * @return the formatted prompt
     */
    private String buildAnalysisPrompt(String code, String context) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("Please analyze this Java code");
        if (context != null && !context.trim().isEmpty()) {
            prompt.append(" (").append(context).append(")");
        }
        prompt.append(":\n\n```java\n").append(code).append("\n```\n\n");
        prompt.append("Provide feedback on:\n");
        prompt.append("- Code quality and structure\n");
        prompt.append("- Potential bugs or issues\n");
        prompt.append("- Performance considerations\n");
        prompt.append("- Best practices and conventions\n");
        prompt.append("- Suggestions for improvement");
        
        return prompt.toString();
    }
    
    /**
     * Builds a prompt for code generation.
     * @param description the description of what to generate
     * @param context additional context
     * @return the formatted prompt
     */
    private String buildGenerationPrompt(String description, String context) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("Generate Java code for: ").append(description).append("\n\n");
        if (context != null && !context.trim().isEmpty()) {
            prompt.append("Context: ").append(context).append("\n\n");
        }
        prompt.append("Requirements:\n");
        prompt.append("- Follow Java naming conventions\n");
        prompt.append("- Include appropriate comments and documentation\n");
        prompt.append("- Handle exceptions appropriately\n");
        prompt.append("- Use modern Java features when appropriate\n");
        prompt.append("- Ensure code is clean and maintainable");
        
        return prompt.toString();
    }
}
