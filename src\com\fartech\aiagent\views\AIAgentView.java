package com.fartech.aiagent.views;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IActionBars;
import org.eclipse.ui.part.ViewPart;

import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.services.AIAgentService;

/**
 * AI Agent view for interacting with the AI assistant.
 */
public class AIAgentView extends ViewPart {
    
    public static final String ID = "com.fartech.aiagent.views.AIAgentView";
    
    private Text inputText;
    private StyledText outputText;
    private Button sendButton;
    private Button clearButton;
    private Label statusLabel;
    
    private AIAgentService aiService;
    
    private Action analyzeAction;
    private Action generateAction;
    private Action explainAction;
    private Action configureAction;
    
    @Override
    public void createPartControl(Composite parent) {
        aiService = AIAgentService.getInstance();
        
        // Create main container
        SashForm sashForm = new SashForm(parent, SWT.VERTICAL);
        
        // Create input section
        createInputSection(sashForm);
        
        // Create output section
        createOutputSection(sashForm);
        
        // Set sash weights (30% input, 70% output)
        sashForm.setWeights(new int[] { 30, 70 });
        
        // Create actions
        createActions();
        
        // Contribute to action bars
        contributeToActionBars();
        
        // Update status
        updateStatus();
    }
    
    private void createInputSection(Composite parent) {
        Composite inputComposite = new Composite(parent, SWT.NONE);
        inputComposite.setLayout(new GridLayout(1, false));
        
        // Input label
        Label inputLabel = new Label(inputComposite, SWT.NONE);
        inputLabel.setText("Ask the AI Assistant:");
        
        // Input text area
        inputText = new Text(inputComposite, SWT.BORDER | SWT.MULTI | SWT.WRAP | SWT.V_SCROLL);
        inputText.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        inputText.setMessage("Type your question or request here...");
        
        // Add key listener for Ctrl+Enter
        inputText.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if ((e.stateMask & SWT.CTRL) != 0 && e.keyCode == SWT.CR) {
                    sendRequest();
                }
            }
        });
        
        // Button composite
        Composite buttonComposite = new Composite(inputComposite, SWT.NONE);
        buttonComposite.setLayout(new GridLayout(3, false));
        buttonComposite.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        
        // Send button
        sendButton = new Button(buttonComposite, SWT.PUSH);
        sendButton.setText("Send (Ctrl+Enter)");
        sendButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                sendRequest();
            }
        });
        
        // Clear button
        clearButton = new Button(buttonComposite, SWT.PUSH);
        clearButton.setText("Clear");
        clearButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                clearConversation();
            }
        });
        
        // Status label
        statusLabel = new Label(buttonComposite, SWT.NONE);
        statusLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
    }
    
    private void createOutputSection(Composite parent) {
        Composite outputComposite = new Composite(parent, SWT.NONE);
        outputComposite.setLayout(new GridLayout(1, false));
        
        // Output label
        Label outputLabel = new Label(outputComposite, SWT.NONE);
        outputLabel.setText("AI Assistant Response:");
        
        // Output text area
        outputText = new StyledText(outputComposite, SWT.BORDER | SWT.MULTI | SWT.WRAP | SWT.V_SCROLL | SWT.READ_ONLY);
        outputText.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        outputText.setBackground(outputComposite.getDisplay().getSystemColor(SWT.COLOR_WHITE));
        outputText.setText("Welcome to the AI Assistant! Ask me anything about your code.\n\nYou can:\n- Ask questions about Java code\n- Request code analysis\n- Generate new code\n- Get explanations of existing code\n- And much more!\n\nMake sure to configure your AI provider in the preferences first.");
    }
    
    private void createActions() {
        analyzeAction = new Action() {
            public void run() {
                analyzeSelectedCode();
            }
        };
        analyzeAction.setText("Analyze Code");
        analyzeAction.setToolTipText("Analyze selected code");
        
        generateAction = new Action() {
            public void run() {
                generateCode();
            }
        };
        generateAction.setText("Generate Code");
        generateAction.setToolTipText("Generate code based on description");
        
        explainAction = new Action() {
            public void run() {
                explainSelectedCode();
            }
        };
        explainAction.setText("Explain Code");
        explainAction.setToolTipText("Explain selected code");
        
        configureAction = new Action() {
            public void run() {
                openPreferences();
            }
        };
        configureAction.setText("Configure");
        configureAction.setToolTipText("Configure AI provider settings");
    }
    
    private void contributeToActionBars() {
        IActionBars bars = getViewSite().getActionBars();
        fillLocalPullDown(bars.getMenuManager());
        fillLocalToolBar(bars.getToolBarManager());
    }
    
    private void fillLocalPullDown(IMenuManager manager) {
        manager.add(analyzeAction);
        manager.add(generateAction);
        manager.add(explainAction);
        manager.add(new Separator());
        manager.add(configureAction);
    }
    
    private void fillLocalToolBar(IToolBarManager manager) {
        manager.add(analyzeAction);
        manager.add(generateAction);
        manager.add(explainAction);
        manager.add(new Separator());
        manager.add(configureAction);
    }
    
    private void sendRequest() {
        String input = inputText.getText().trim();
        if (input.isEmpty()) {
            return;
        }
        
        if (!aiService.isReady()) {
            MessageDialog.openWarning(getSite().getShell(), "AI Agent Not Configured", 
                "Please configure an AI provider in the preferences before using the AI Assistant.");
            return;
        }
        
        // Disable UI while processing
        setUIEnabled(false);
        statusLabel.setText("Processing request...");
        
        // Process in background thread
        Thread requestThread = new Thread(() -> {
            try {
                // Use a general request that can handle various types of input
                AIResponse response = aiService.analyzeCode(input, "User question or request");

                // Update UI in main thread
                getSite().getShell().getDisplay().asyncExec(() -> {
                    if (response.isSuccess()) {
                        appendToOutput("You: " + input + "\n\n");
                        appendToOutput("AI Assistant: " + response.getContent() + "\n\n");
                        appendToOutput("---\n\n");
                        inputText.setText("");
                        statusLabel.setText("Response received");
                    } else {
                        appendToOutput("Error: " + response.getErrorMessage() + "\n\n");
                        statusLabel.setText("Request failed");
                    }
                    setUIEnabled(true);
                });

            } catch (Exception e) {
                getSite().getShell().getDisplay().asyncExec(() -> {
                    appendToOutput("Error: " + e.getMessage() + "\n\n");
                    statusLabel.setText("Request failed");
                    setUIEnabled(true);
                });
            }
        });
        requestThread.start();
    }
    
    private void clearConversation() {
        outputText.setText("Conversation cleared.\n\n");
        statusLabel.setText("Ready");
    }
    
    private void appendToOutput(String text) {
        outputText.append(text);
        outputText.setTopIndex(outputText.getLineCount() - 1);
    }
    
    private void setUIEnabled(boolean enabled) {
        sendButton.setEnabled(enabled);
        inputText.setEnabled(enabled);
    }
    
    private void updateStatus() {
        if (aiService.isReady()) {
            statusLabel.setText("Ready - " + aiService.getCurrentProvider().getDisplayName());
        } else {
            statusLabel.setText("Not configured - click Configure to set up AI provider");
        }
    }
    
    private void analyzeSelectedCode() {
        // This would integrate with the Eclipse editor to get selected text
        MessageDialog.openInformation(getSite().getShell(), "Analyze Code", 
            "This feature will analyze selected code in the editor. Implementation pending.");
    }
    
    private void generateCode() {
        // This would open a dialog for code generation
        MessageDialog.openInformation(getSite().getShell(), "Generate Code", 
            "This feature will generate code based on your description. Implementation pending.");
    }
    
    private void explainSelectedCode() {
        // This would integrate with the Eclipse editor to get selected text
        MessageDialog.openInformation(getSite().getShell(), "Explain Code", 
            "This feature will explain selected code in the editor. Implementation pending.");
    }
    
    private void openPreferences() {
        try {
            // Open the preferences dialog
            org.eclipse.ui.dialogs.PreferencesUtil.createPreferenceDialogOn(
                getSite().getShell(), 
                "com.fartech.aiagent.preferences.AIAgentPreferencePage", 
                null, 
                null
            ).open();
            
            // Refresh the service after preferences might have changed
            aiService.refreshProvider();
            updateStatus();
        } catch (Exception e) {
            MessageDialog.openError(getSite().getShell(), "Error", 
                "Failed to open preferences: " + e.getMessage());
        }
    }
    
    @Override
    public void setFocus() {
        inputText.setFocus();
    }
}
