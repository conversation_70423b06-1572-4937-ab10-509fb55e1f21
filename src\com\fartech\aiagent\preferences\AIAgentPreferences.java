package com.fartech.aiagent.preferences;

import java.util.Map;
import java.util.HashMap;

import org.eclipse.core.runtime.preferences.InstanceScope;
import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.ui.preferences.ScopedPreferenceStore;

import com.fartech.aiagent.Activator;

/**
 * Utility class for managing AI Agent preferences.
 */
public class AIAgentPreferences {
    
    // Preference keys
    private static final String SELECTED_PROVIDER = "selected_provider";
    private static final String PROVIDER_CONFIG_PREFIX = "provider_config_";
    
    private static IPreferenceStore preferenceStore;
    
    /**
     * Gets the preference store for AI Agent.
     * @return the preference store
     */
    public static IPreferenceStore getPreferenceStore() {
        if (preferenceStore == null) {
            preferenceStore = new ScopedPreferenceStore(InstanceScope.INSTANCE, Activator.PLUGIN_ID);
        }
        return preferenceStore;
    }
    
    /**
     * Gets the currently selected AI provider ID.
     * @return the provider ID, or null if none selected
     */
    public static String getSelectedProvider() {
        String providerId = getPreferenceStore().getString(SELECTED_PROVIDER);
        return providerId.isEmpty() ? null : providerId;
    }
    
    /**
     * Sets the currently selected AI provider ID.
     * @param providerId the provider ID
     */
    public static void setSelectedProvider(String providerId) {
        getPreferenceStore().setValue(SELECTED_PROVIDER, providerId != null ? providerId : "");
    }
    
    /**
     * Gets the configuration for a specific provider.
     * @param providerId the provider ID
     * @return the configuration map
     */
    public static Map<String, String> getProviderConfiguration(String providerId) {
        Map<String, String> config = new HashMap<>();
        if (providerId == null) {
            return config;
        }
        
        String prefix = PROVIDER_CONFIG_PREFIX + providerId + "_";
        IPreferenceStore store = getPreferenceStore();
        
        // Get all preference names and filter for this provider
        try {
            // This is a simplified approach - in a real implementation,
            // you might want to store the configuration keys separately
            String[] commonKeys = {"api_key", "base_url", "model", "organization", "version",
                                   "available_models", "api_format", "auth_header", "endpoint_path"};

            for (String key : commonKeys) {
                String prefKey = prefix + key;
                String value = store.getString(prefKey);
                if (!value.isEmpty()) {
                    config.put(key, value);
                }
            }
        } catch (Exception e) {
            // Log error but don't fail
            System.err.println("Error loading provider configuration: " + e.getMessage());
        }
        
        return config;
    }
    
    /**
     * Sets the configuration for a specific provider.
     * @param providerId the provider ID
     * @param configuration the configuration map
     */
    public static void setProviderConfiguration(String providerId, Map<String, String> configuration) {
        if (providerId == null || configuration == null) {
            return;
        }
        
        String prefix = PROVIDER_CONFIG_PREFIX + providerId + "_";
        IPreferenceStore store = getPreferenceStore();
        
        // Clear existing configuration for this provider
        clearProviderConfiguration(providerId);
        
        // Set new configuration
        for (Map.Entry<String, String> entry : configuration.entrySet()) {
            String prefKey = prefix + entry.getKey();
            store.setValue(prefKey, entry.getValue());
        }
    }
    
    /**
     * Clears the configuration for a specific provider.
     * @param providerId the provider ID
     */
    public static void clearProviderConfiguration(String providerId) {
        if (providerId == null) {
            return;
        }
        
        String prefix = PROVIDER_CONFIG_PREFIX + providerId + "_";
        IPreferenceStore store = getPreferenceStore();
        
        // Clear common configuration keys
        String[] commonKeys = {"api_key", "base_url", "model", "organization", "version",
                               "available_models", "api_format", "auth_header", "endpoint_path"};
        for (String key : commonKeys) {
            String prefKey = prefix + key;
            store.setToDefault(prefKey);
        }
    }
    
    /**
     * Gets a specific configuration value for a provider.
     * @param providerId the provider ID
     * @param key the configuration key
     * @return the configuration value, or null if not found
     */
    public static String getProviderConfigurationValue(String providerId, String key) {
        if (providerId == null || key == null) {
            return null;
        }
        
        String prefKey = PROVIDER_CONFIG_PREFIX + providerId + "_" + key;
        String value = getPreferenceStore().getString(prefKey);
        return value.isEmpty() ? null : value;
    }
    
    /**
     * Sets a specific configuration value for a provider.
     * @param providerId the provider ID
     * @param key the configuration key
     * @param value the configuration value
     */
    public static void setProviderConfigurationValue(String providerId, String key, String value) {
        if (providerId == null || key == null) {
            return;
        }
        
        String prefKey = PROVIDER_CONFIG_PREFIX + providerId + "_" + key;
        getPreferenceStore().setValue(prefKey, value != null ? value : "");
    }
    
    /**
     * Checks if a provider has any configuration stored.
     * @param providerId the provider ID
     * @return true if the provider has configuration, false otherwise
     */
    public static boolean hasProviderConfiguration(String providerId) {
        Map<String, String> config = getProviderConfiguration(providerId);
        return !config.isEmpty();
    }
    
    /**
     * Resets all AI Agent preferences to defaults.
     */
    public static void resetToDefaults() {
        IPreferenceStore store = getPreferenceStore();
        store.setToDefault(SELECTED_PROVIDER);
        
        // Clear all provider configurations
        // This is a simplified approach - in a real implementation,
        // you might want to track which providers have been configured
        String[] providerIds = {"openai", "anthropic", "custom"};
        for (String providerId : providerIds) {
            clearProviderConfiguration(providerId);
        }
    }
}
