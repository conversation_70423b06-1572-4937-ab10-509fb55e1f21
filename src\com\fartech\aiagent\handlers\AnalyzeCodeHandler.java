package com.fartech.aiagent.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.handlers.HandlerUtil;
import org.eclipse.ui.texteditor.ITextEditor;

import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.services.AIAgentService;

/**
 * Handler for the "Analyze Code" command.
 */
public class AnalyzeCodeHandler extends AbstractHandler {
    
    @Override
    public Object execute(ExecutionEvent event) throws ExecutionException {
        IWorkbenchWindow window = HandlerUtil.getActiveWorkbenchWindowChecked(event);
        
        try {
            // Get the active editor
            IEditorPart editor = window.getActivePage().getActiveEditor();
            if (!(editor instanceof ITextEditor)) {
                MessageDialog.openInformation(window.getShell(), "Analyze Code", 
                    "Please select text in a code editor to analyze.");
                return null;
            }
            
            ITextEditor textEditor = (ITextEditor) editor;
            ISelection selection = textEditor.getSelectionProvider().getSelection();
            
            if (!(selection instanceof ITextSelection)) {
                MessageDialog.openInformation(window.getShell(), "Analyze Code", 
                    "Please select some code to analyze.");
                return null;
            }
            
            ITextSelection textSelection = (ITextSelection) selection;
            String selectedText = textSelection.getText();
            
            if (selectedText == null || selectedText.trim().isEmpty()) {
                MessageDialog.openInformation(window.getShell(), "Analyze Code", 
                    "Please select some code to analyze.");
                return null;
            }
            
            // Get AI service
            AIAgentService aiService = AIAgentService.getInstance();
            if (!aiService.isReady()) {
                MessageDialog.openWarning(window.getShell(), "AI Agent Not Configured", 
                    "Please configure an AI provider in the preferences before using the AI Assistant.");
                return null;
            }
            
            // Show progress dialog and analyze in background
            analyzeCodeInBackground(window, selectedText, aiService);
            
        } catch (Exception e) {
            MessageDialog.openError(window.getShell(), "Error", 
                "Failed to analyze code: " + e.getMessage());
        }
        
        return null;
    }
    
    private void analyzeCodeInBackground(IWorkbenchWindow window, String code, AIAgentService aiService) {
        Thread analysisThread = new Thread(() -> {
            try {
                AIResponse response = aiService.analyzeCode(code, "Selected code from editor");
                
                // Update UI in main thread
                window.getShell().getDisplay().asyncExec(() -> {
                    if (response.isSuccess()) {
                        showAnalysisResult(window, code, response.getContent());
                    } else {
                        MessageDialog.openError(window.getShell(), "Analysis Failed", 
                            "Failed to analyze code: " + response.getErrorMessage());
                    }
                });
                
            } catch (Exception e) {
                window.getShell().getDisplay().asyncExec(() -> {
                    MessageDialog.openError(window.getShell(), "Analysis Failed", 
                        "Failed to analyze code: " + e.getMessage());
                });
            }
        });
        analysisThread.start();
    }
    
    private void showAnalysisResult(IWorkbenchWindow window, String originalCode, String analysis) {
        // Create a simple dialog to show the analysis result
        // In a more sophisticated implementation, you might want to show this in a dedicated view
        String message = "Code Analysis Result:\n\n" + analysis;
        
        MessageDialog dialog = new MessageDialog(
            window.getShell(),
            "Code Analysis Result",
            null,
            message,
            MessageDialog.INFORMATION,
            new String[] { "OK", "Copy to Clipboard" },
            0
        );
        
        int result = dialog.open();
        if (result == 1) { // Copy to Clipboard
            copyToClipboard(window, analysis);
        }
    }
    
    private void copyToClipboard(IWorkbenchWindow window, String text) {
        try {
            org.eclipse.swt.dnd.Clipboard clipboard = new org.eclipse.swt.dnd.Clipboard(window.getShell().getDisplay());
            org.eclipse.swt.dnd.TextTransfer textTransfer = org.eclipse.swt.dnd.TextTransfer.getInstance();
            clipboard.setContents(new Object[] { text }, new org.eclipse.swt.dnd.Transfer[] { textTransfer });
            clipboard.dispose();
        } catch (Exception e) {
            // Ignore clipboard errors
        }
    }
}
