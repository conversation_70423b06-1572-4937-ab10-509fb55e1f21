package com.fartech.aiagent.api;

/**
 * Exception thrown by AI providers when operations fail.
 */
public class AIProviderException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private String providerName;
    private int errorCode;
    
    public AIProviderException(String message) {
        super(message);
    }
    
    public AIProviderException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public AIProviderException(String providerName, String message) {
        super(message);
        this.providerName = providerName;
    }
    
    public AIProviderException(String providerName, String message, Throwable cause) {
        super(message, cause);
        this.providerName = providerName;
    }
    
    public AIProviderException(String providerName, int errorCode, String message) {
        super(message);
        this.providerName = providerName;
        this.errorCode = errorCode;
    }
    
    public AIProviderException(String providerName, int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.providerName = providerName;
        this.errorCode = errorCode;
    }
    
    public String getProviderName() {
        return providerName;
    }
    
    public int getErrorCode() {
        return errorCode;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AIProviderException");
        if (providerName != null) {
            sb.append(" [").append(providerName).append("]");
        }
        if (errorCode != 0) {
            sb.append(" (").append(errorCode).append(")");
        }
        sb.append(": ").append(getMessage());
        return sb.toString();
    }
}
