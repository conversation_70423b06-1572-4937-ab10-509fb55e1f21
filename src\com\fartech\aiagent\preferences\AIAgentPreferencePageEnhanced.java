package com.fartech.aiagent.preferences;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import org.eclipse.jface.preference.PreferencePage;
import org.eclipse.jface.viewers.ArrayContentProvider;
import org.eclipse.jface.viewers.ComboViewer;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IWorkbench;
import org.eclipse.ui.IWorkbenchPreferencePage;

import com.fartech.aiagent.api.AIProvider;
import com.fartech.aiagent.providers.AIProviderRegistry;
import com.fartech.aiagent.providers.CustomProvider;

/**
 * Enhanced preference page for configuring AI Agent settings with custom provider support.
 */
public class AIAgentPreferencePageEnhanced extends PreferencePage implements IWorkbenchPreferencePage {
    
    private ComboViewer providerCombo;
    private Group configGroup;
    private Map<String, Control> configFields;
    private Button testButton;
    private Label statusLabel;
    private Combo modelCombo;
    private Label modelLabel;
    
    private AIProviderRegistry registry;
    private AIProvider currentProvider;
    
    public AIAgentPreferencePageEnhanced() {
        super();
        setPreferenceStore(AIAgentPreferences.getPreferenceStore());
        setDescription("Configure AI Agent settings and providers");
        this.registry = AIProviderRegistry.getInstance();
        this.configFields = new HashMap<>();
    }
    
    @Override
    public void init(IWorkbench workbench) {
        // Initialize if needed
    }
    
    @Override
    protected Control createContents(Composite parent) {
        Composite composite = new Composite(parent, SWT.NONE);
        composite.setLayout(new GridLayout(1, false));
        
        createProviderSelection(composite);
        createConfigurationSection(composite);
        createTestSection(composite);
        
        loadPreferences();
        updateConfigurationFields();
        
        return composite;
    }
    
    private void createProviderSelection(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("AI Provider");
        group.setLayout(new GridLayout(2, false));
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        
        Label label = new Label(group, SWT.NONE);
        label.setText("Provider:");
        
        providerCombo = new ComboViewer(group, SWT.READ_ONLY);
        providerCombo.getControl().setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        providerCombo.setContentProvider(ArrayContentProvider.getInstance());
        providerCombo.setLabelProvider(new LabelProvider() {
            @Override
            public String getText(Object element) {
                if (element instanceof AIProvider) {
                    AIProvider provider = (AIProvider) element;
                    return provider.getDisplayName() + " - " + provider.getDescription();
                }
                return super.getText(element);
            }
        });
        
        List<AIProvider> providers = registry.getProviders();
        providerCombo.setInput(providers);
        
        providerCombo.addSelectionChangedListener(new ISelectionChangedListener() {
            @Override
            public void selectionChanged(SelectionChangedEvent event) {
                IStructuredSelection selection = (IStructuredSelection) event.getSelection();
                if (!selection.isEmpty()) {
                    currentProvider = (AIProvider) selection.getFirstElement();
                    updateConfigurationFields();
                }
            }
        });
    }
    
    private void createConfigurationSection(Composite parent) {
        configGroup = new Group(parent, SWT.NONE);
        configGroup.setText("Configuration");
        configGroup.setLayout(new GridLayout(2, false));
        configGroup.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
    }
    
    private void createTestSection(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("Test Configuration");
        group.setLayout(new GridLayout(2, false));
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        
        testButton = new Button(group, SWT.PUSH);
        testButton.setText("Test Connection");
        testButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                testConfiguration();
            }
        });
        
        statusLabel = new Label(group, SWT.NONE);
        statusLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        statusLabel.setText("Click 'Test Connection' to validate your configuration");
    }
    
    private void updateConfigurationFields() {
        // Clear existing fields
        for (Control child : configGroup.getChildren()) {
            child.dispose();
        }
        configFields.clear();
        
        if (currentProvider == null) {
            configGroup.layout();
            return;
        }
        
        // Create fields for required configuration keys
        String[] requiredKeys = currentProvider.getRequiredConfigurationKeys();
        for (String key : requiredKeys) {
            createConfigField(key, true);
        }
        
        // Create fields for optional configuration keys
        String[] optionalKeys = currentProvider.getOptionalConfigurationKeys();
        for (String key : optionalKeys) {
            createConfigField(key, false);
        }
        
        configGroup.layout();
        configGroup.getParent().layout();
    }
    
    private void createConfigField(String key, boolean required) {
        Label label = new Label(configGroup, SWT.NONE);
        String labelText = formatKeyName(key);
        if (required) {
            labelText += " *";
        }
        label.setText(labelText + ":");
        
        Control control;
        
        // Special handling for model field when it's a custom provider
        if ("model".equals(key) && currentProvider instanceof CustomProvider) {
            control = createModelDropdown();
        } else if ("available_models".equals(key)) {
            control = createAvailableModelsField();
        } else if ("api_format".equals(key)) {
            control = createApiFormatDropdown();
        } else {
            control = createTextField(key);
        }
        
        control.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        
        // Add tooltip with help text
        String help = currentProvider.getConfigurationHelp(key);
        if (help != null && !help.isEmpty()) {
            control.setToolTipText(help);
            label.setToolTipText(help);
        }
        
        configFields.put(key, control);
    }
    
    private Control createTextField(String key) {
        Text text = new Text(configGroup, SWT.BORDER);
        if (key.toLowerCase().contains("key") || key.toLowerCase().contains("password")) {
            text.setEchoChar('*');
        }
        
        // Add modify listener to clear status when configuration changes
        text.addModifyListener(new ModifyListener() {
            @Override
            public void modifyText(ModifyEvent e) {
                statusLabel.setText("Configuration changed - click 'Test Connection' to validate");
                
                // If this is the available_models field, update the model dropdown
                if ("available_models".equals(key)) {
                    updateModelDropdown();
                }
            }
        });
        
        return text;
    }
    
    private Control createModelDropdown() {
        modelCombo = new Combo(configGroup, SWT.READ_ONLY);
        updateModelDropdown();
        
        modelCombo.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                statusLabel.setText("Configuration changed - click 'Test Connection' to validate");
            }
        });
        
        return modelCombo;
    }
    
    private Control createAvailableModelsField() {
        Text text = new Text(configGroup, SWT.BORDER);
        text.setMessage("model1,model2,model3");
        
        text.addModifyListener(new ModifyListener() {
            @Override
            public void modifyText(ModifyEvent e) {
                statusLabel.setText("Configuration changed - click 'Test Connection' to validate");
                updateModelDropdown();
            }
        });
        
        return text;
    }
    
    private Control createApiFormatDropdown() {
        Combo combo = new Combo(configGroup, SWT.READ_ONLY);
        combo.setItems(new String[] { "openai", "anthropic" });
        combo.select(0); // Default to OpenAI format
        
        combo.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                statusLabel.setText("Configuration changed - click 'Test Connection' to validate");
            }
        });
        
        return combo;
    }
    
    private void updateModelDropdown() {
        if (modelCombo == null || modelCombo.isDisposed()) {
            return;
        }
        
        // Get available models from the text field
        Control availableModelsControl = configFields.get("available_models");
        if (availableModelsControl instanceof Text) {
            Text availableModelsText = (Text) availableModelsControl;
            String modelsText = availableModelsText.getText();
            
            if (modelsText != null && !modelsText.trim().isEmpty()) {
                String[] models = modelsText.split(",");
                String[] trimmedModels = new String[models.length];
                for (int i = 0; i < models.length; i++) {
                    trimmedModels[i] = models[i].trim();
                }
                
                String currentSelection = modelCombo.getText();
                modelCombo.setItems(trimmedModels);
                
                // Try to restore previous selection
                int index = modelCombo.indexOf(currentSelection);
                if (index >= 0) {
                    modelCombo.select(index);
                } else if (trimmedModels.length > 0) {
                    modelCombo.select(0);
                }
            } else {
                modelCombo.setItems(new String[] { "default-model" });
                modelCombo.select(0);
            }
        }
    }
    
    private String formatKeyName(String key) {
        // Convert snake_case to Title Case
        String[] parts = key.split("_");
        StringBuilder sb = new StringBuilder();
        for (String part : parts) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            sb.append(Character.toUpperCase(part.charAt(0)));
            if (part.length() > 1) {
                sb.append(part.substring(1).toLowerCase());
            }
        }
        return sb.toString();
    }
    
    private void testConfiguration() {
        if (currentProvider == null) {
            statusLabel.setText("No provider selected");
            return;
        }
        
        try {
            // Get configuration from fields
            Map<String, String> config = new HashMap<>();
            for (Map.Entry<String, Control> entry : configFields.entrySet()) {
                String key = entry.getKey();
                Control control = entry.getValue();
                String value = getControlValue(control);
                
                if (value != null && !value.trim().isEmpty()) {
                    config.put(key, value);
                }
            }
            
            // Create a new provider instance for testing
            AIProvider testProvider = registry.createProviderInstance(currentProvider.getProviderId());
            testProvider.configure(config);
            
            statusLabel.setText("Testing connection...");
            testButton.setEnabled(false);
            
            // Test in a separate thread to avoid blocking UI
            Thread testThread = new Thread(() -> {
                try {
                    boolean valid = testProvider.validateConfiguration();
                    
                    // Update UI in the main thread
                    configGroup.getDisplay().asyncExec(() -> {
                        if (valid) {
                            statusLabel.setText("✓ Connection successful!");
                        } else {
                            statusLabel.setText("✗ Connection failed");
                        }
                        testButton.setEnabled(true);
                    });
                } catch (Exception e) {
                    configGroup.getDisplay().asyncExec(() -> {
                        statusLabel.setText("✗ Error: " + e.getMessage());
                        testButton.setEnabled(true);
                    });
                }
            });
            testThread.start();
            
        } catch (Exception e) {
            statusLabel.setText("✗ Configuration error: " + e.getMessage());
        }
    }
    
    private String getControlValue(Control control) {
        if (control instanceof Text) {
            return ((Text) control).getText();
        } else if (control instanceof Combo) {
            return ((Combo) control).getText();
        }
        return null;
    }
    
    private void loadPreferences() {
        String providerId = AIAgentPreferences.getSelectedProvider();
        if (providerId != null) {
            AIProvider provider = registry.getProvider(providerId);
            if (provider != null) {
                currentProvider = provider;
                providerCombo.setSelection(new StructuredSelection(provider));
            }
        }
    }
    
    @Override
    protected void performDefaults() {
        // Clear all configuration fields
        for (Control control : configFields.values()) {
            if (control instanceof Text) {
                ((Text) control).setText("");
            } else if (control instanceof Combo) {
                ((Combo) control).deselectAll();
            }
        }
        statusLabel.setText("Click 'Test Connection' to validate your configuration");
        super.performDefaults();
    }
    
    @Override
    public boolean performOk() {
        if (currentProvider != null) {
            // Save provider selection
            AIAgentPreferences.setSelectedProvider(currentProvider.getProviderId());
            
            // Save configuration
            Map<String, String> config = new HashMap<>();
            for (Map.Entry<String, Control> entry : configFields.entrySet()) {
                String key = entry.getKey();
                String value = getControlValue(entry.getValue());
                if (value != null) {
                    config.put(key, value);
                }
            }
            AIAgentPreferences.setProviderConfiguration(currentProvider.getProviderId(), config);
        }
        
        return super.performOk();
    }
}
