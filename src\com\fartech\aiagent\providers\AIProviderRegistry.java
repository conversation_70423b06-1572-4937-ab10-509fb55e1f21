package com.fartech.aiagent.providers;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

import com.fartech.aiagent.api.AIProvider;
import com.fartech.aiagent.api.AIProviderException;

/**
 * Registry for managing AI providers.
 */
public class AIProviderRegistry {
    
    private static AIProviderRegistry instance;
    private Map<String, AIProvider> providers;
    
    private AIProviderRegistry() {
        this.providers = new HashMap<>();
        registerDefaultProviders();
    }
    
    public static synchronized AIProviderRegistry getInstance() {
        if (instance == null) {
            instance = new AIProviderRegistry();
        }
        return instance;
    }
    
    /**
     * Registers the default AI providers.
     */
    private void registerDefaultProviders() {
        registerProvider(new OpenAIProvider());
        registerProvider(new AnthropicProvider());
        registerProvider(new CustomProvider());
    }
    
    /**
     * Registers an AI provider.
     * @param provider the provider to register
     */
    public void registerProvider(AIProvider provider) {
        if (provider != null) {
            providers.put(provider.getProviderId(), provider);
        }
    }
    
    /**
     * Unregisters an AI provider.
     * @param providerId the ID of the provider to unregister
     */
    public void unregisterProvider(String providerId) {
        providers.remove(providerId);
    }
    
    /**
     * Gets an AI provider by ID.
     * @param providerId the provider ID
     * @return the provider, or null if not found
     */
    public AIProvider getProvider(String providerId) {
        return providers.get(providerId);
    }
    
    /**
     * Gets all registered provider IDs.
     * @return list of provider IDs
     */
    public List<String> getProviderIds() {
        return new ArrayList<>(providers.keySet());
    }
    
    /**
     * Gets all registered providers.
     * @return list of providers
     */
    public List<AIProvider> getProviders() {
        return new ArrayList<>(providers.values());
    }
    
    /**
     * Checks if a provider is registered.
     * @param providerId the provider ID
     * @return true if registered, false otherwise
     */
    public boolean isProviderRegistered(String providerId) {
        return providers.containsKey(providerId);
    }
    
    /**
     * Gets the number of registered providers.
     * @return the number of providers
     */
    public int getProviderCount() {
        return providers.size();
    }
    
    /**
     * Creates a new instance of a provider by ID.
     * This is useful when you need a fresh instance for configuration.
     * @param providerId the provider ID
     * @return a new instance of the provider, or null if not found
     * @throws AIProviderException if the provider cannot be instantiated
     */
    public AIProvider createProviderInstance(String providerId) throws AIProviderException {
        AIProvider template = providers.get(providerId);
        if (template == null) {
            return null;
        }
        
        try {
            // Create a new instance of the same class
            return template.getClass().newInstance();
        } catch (Exception e) {
            throw new AIProviderException(providerId, "Failed to create provider instance", e);
        }
    }
    
    /**
     * Gets provider information for display purposes.
     * @return map of provider ID to display information
     */
    public Map<String, ProviderInfo> getProviderInfo() {
        Map<String, ProviderInfo> info = new HashMap<>();
        for (AIProvider provider : providers.values()) {
            info.put(provider.getProviderId(), new ProviderInfo(
                provider.getProviderId(),
                provider.getDisplayName(),
                provider.getDescription(),
                provider.isConfigured()
            ));
        }
        return info;
    }
    
    /**
     * Information about a provider for display purposes.
     */
    public static class ProviderInfo {
        private final String id;
        private final String displayName;
        private final String description;
        private final boolean configured;
        
        public ProviderInfo(String id, String displayName, String description, boolean configured) {
            this.id = id;
            this.displayName = displayName;
            this.description = description;
            this.configured = configured;
        }
        
        public String getId() {
            return id;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getDescription() {
            return description;
        }
        
        public boolean isConfigured() {
            return configured;
        }
        
        @Override
        public String toString() {
            return displayName + (configured ? " (Configured)" : " (Not Configured)");
        }
    }
}
