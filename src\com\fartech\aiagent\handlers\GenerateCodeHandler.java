package com.fartech.aiagent.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.jface.dialogs.InputDialog;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.window.Window;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.handlers.HandlerUtil;
import org.eclipse.ui.texteditor.IDocumentProvider;
import org.eclipse.ui.texteditor.ITextEditor;

import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.services.AIAgentService;

/**
 * Handler for the "Generate Code" command.
 */
public class GenerateCodeHandler extends AbstractHandler {
    
    @Override
    public Object execute(ExecutionEvent event) throws ExecutionException {
        IWorkbenchWindow window = HandlerUtil.getActiveWorkbenchWindowChecked(event);
        
        try {
            // Get AI service
            AIAgentService aiService = AIAgentService.getInstance();
            if (!aiService.isReady()) {
                MessageDialog.openWarning(window.getShell(), "AI Agent Not Configured", 
                    "Please configure an AI provider in the preferences before using the AI Assistant.");
                return null;
            }
            
            // Get description from user
            InputDialog dialog = new InputDialog(
                window.getShell(),
                "Generate Code",
                "Describe what code you want to generate:",
                "",
                null
            );
            
            if (dialog.open() != Window.OK) {
                return null;
            }
            
            String description = dialog.getValue();
            if (description == null || description.trim().isEmpty()) {
                return null;
            }
            
            // Get context from current editor if available
            String context = getCurrentEditorContext(window);
            
            // Generate code in background
            generateCodeInBackground(window, description, context, aiService);
            
        } catch (Exception e) {
            MessageDialog.openError(window.getShell(), "Error", 
                "Failed to generate code: " + e.getMessage());
        }
        
        return null;
    }
    
    private String getCurrentEditorContext(IWorkbenchWindow window) {
        try {
            IEditorPart editor = window.getActivePage().getActiveEditor();
            if (editor instanceof ITextEditor) {
                ITextEditor textEditor = (ITextEditor) editor;
                ISelection selection = textEditor.getSelectionProvider().getSelection();
                
                if (selection instanceof ITextSelection) {
                    ITextSelection textSelection = (ITextSelection) selection;
                    String selectedText = textSelection.getText();
                    
                    if (selectedText != null && !selectedText.trim().isEmpty()) {
                        return "Context from current selection: " + selectedText;
                    }
                }
                
                // Get file name as context
                String fileName = editor.getEditorInput().getName();
                return "Current file: " + fileName;
            }
        } catch (Exception e) {
            // Ignore errors getting context
        }
        
        return null;
    }
    
    private void generateCodeInBackground(IWorkbenchWindow window, String description, String context, AIAgentService aiService) {
        Thread generationThread = new Thread(() -> {
            try {
                AIResponse response = aiService.generateCode(description, context);
                
                // Update UI in main thread
                window.getShell().getDisplay().asyncExec(() -> {
                    if (response.isSuccess()) {
                        showGeneratedCode(window, description, response.getContent());
                    } else {
                        MessageDialog.openError(window.getShell(), "Code Generation Failed", 
                            "Failed to generate code: " + response.getErrorMessage());
                    }
                });
                
            } catch (Exception e) {
                window.getShell().getDisplay().asyncExec(() -> {
                    MessageDialog.openError(window.getShell(), "Code Generation Failed", 
                        "Failed to generate code: " + e.getMessage());
                });
            }
        });
        generationThread.start();
    }
    
    private void showGeneratedCode(IWorkbenchWindow window, String description, String generatedCode) {
        // Create a dialog to show the generated code with options
        String message = "Generated code for: " + description + "\n\n" + generatedCode;
        
        MessageDialog dialog = new MessageDialog(
            window.getShell(),
            "Generated Code",
            null,
            message,
            MessageDialog.INFORMATION,
            new String[] { "Insert at Cursor", "Copy to Clipboard", "Cancel" },
            0
        );
        
        int result = dialog.open();
        switch (result) {
            case 0: // Insert at cursor
                insertCodeAtCursor(window, generatedCode);
                break;
            case 1: // Copy to clipboard
                copyToClipboard(window, generatedCode);
                break;
            default:
                // Cancel - do nothing
                break;
        }
    }
    
    private void insertCodeAtCursor(IWorkbenchWindow window, String code) {
        try {
            IEditorPart editor = window.getActivePage().getActiveEditor();
            if (!(editor instanceof ITextEditor)) {
                MessageDialog.openInformation(window.getShell(), "Insert Code", 
                    "Please open a text editor to insert the generated code.");
                return;
            }
            
            ITextEditor textEditor = (ITextEditor) editor;
            ISelection selection = textEditor.getSelectionProvider().getSelection();
            
            if (selection instanceof ITextSelection) {
                ITextSelection textSelection = (ITextSelection) selection;
                
                IDocumentProvider provider = textEditor.getDocumentProvider();
                IDocument document = provider.getDocument(textEditor.getEditorInput());
                
                // Insert the code at the current cursor position
                int offset = textSelection.getOffset();
                document.replace(offset, textSelection.getLength(), code);
                
                MessageDialog.openInformation(window.getShell(), "Code Inserted", 
                    "Generated code has been inserted at the cursor position.");
            }
            
        } catch (Exception e) {
            MessageDialog.openError(window.getShell(), "Insert Failed", 
                "Failed to insert code: " + e.getMessage());
        }
    }
    
    private void copyToClipboard(IWorkbenchWindow window, String text) {
        try {
            org.eclipse.swt.dnd.Clipboard clipboard = new org.eclipse.swt.dnd.Clipboard(window.getShell().getDisplay());
            org.eclipse.swt.dnd.TextTransfer textTransfer = org.eclipse.swt.dnd.TextTransfer.getInstance();
            clipboard.setContents(new Object[] { text }, new org.eclipse.swt.dnd.Transfer[] { textTransfer });
            clipboard.dispose();
            
            MessageDialog.openInformation(window.getShell(), "Copied", 
                "Generated code has been copied to the clipboard.");
        } catch (Exception e) {
            MessageDialog.openError(window.getShell(), "Copy Failed", 
                "Failed to copy to clipboard: " + e.getMessage());
        }
    }
}
