package com.fartech.aiagent.utils;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;

import com.fartech.aiagent.Activator;

/**
 * Utility class for logging messages in the AI Agent plugin.
 */
public class Logger {
    
    private static final String PLUGIN_ID = Activator.PLUGIN_ID;
    
    /**
     * Logs an informational message.
     * @param message the message to log
     */
    public static void info(String message) {
        log(IStatus.INFO, message, null);
    }
    
    /**
     * Logs an informational message with context.
     * @param context the context (e.g., class name)
     * @param message the message to log
     */
    public static void info(String context, String message) {
        log(IStatus.INFO, formatMessage(context, message), null);
    }
    
    /**
     * Logs a warning message.
     * @param message the message to log
     */
    public static void warning(String message) {
        log(IStatus.WARNING, message, null);
    }
    
    /**
     * Logs a warning message with context.
     * @param context the context (e.g., class name)
     * @param message the message to log
     */
    public static void warning(String context, String message) {
        log(IStatus.WARNING, formatMessage(context, message), null);
    }
    
    /**
     * Logs a warning message with throwable.
     * @param message the message to log
     * @param throwable the throwable
     */
    public static void warning(String message, Throwable throwable) {
        log(IStatus.WARNING, message, throwable);
    }
    
    /**
     * Logs a warning message with context and throwable.
     * @param context the context (e.g., class name)
     * @param message the message to log
     * @param throwable the throwable
     */
    public static void warning(String context, String message, Throwable throwable) {
        log(IStatus.WARNING, formatMessage(context, message), throwable);
    }
    
    /**
     * Logs an error message.
     * @param message the message to log
     */
    public static void error(String message) {
        log(IStatus.ERROR, message, null);
    }
    
    /**
     * Logs an error message with context.
     * @param context the context (e.g., class name)
     * @param message the message to log
     */
    public static void error(String context, String message) {
        log(IStatus.ERROR, formatMessage(context, message), null);
    }
    
    /**
     * Logs an error message with throwable.
     * @param message the message to log
     * @param throwable the throwable
     */
    public static void error(String message, Throwable throwable) {
        log(IStatus.ERROR, message, throwable);
    }
    
    /**
     * Logs an error message with context and throwable.
     * @param context the context (e.g., class name)
     * @param message the message to log
     * @param throwable the throwable
     */
    public static void error(String context, String message, Throwable throwable) {
        log(IStatus.ERROR, formatMessage(context, message), throwable);
    }
    
    /**
     * Logs a debug message (only in debug mode).
     * @param message the message to log
     */
    public static void debug(String message) {
        if (isDebugEnabled()) {
            log(IStatus.INFO, "[DEBUG] " + message, null);
        }
    }
    
    /**
     * Logs a debug message with context (only in debug mode).
     * @param context the context (e.g., class name)
     * @param message the message to log
     */
    public static void debug(String context, String message) {
        if (isDebugEnabled()) {
            log(IStatus.INFO, "[DEBUG] " + formatMessage(context, message), null);
        }
    }
    
    /**
     * Logs an exception with full stack trace.
     * @param context the context where the exception occurred
     * @param throwable the exception
     */
    public static void exception(String context, Throwable throwable) {
        String message = "Exception in " + context;
        if (throwable.getMessage() != null) {
            message += ": " + throwable.getMessage();
        }
        log(IStatus.ERROR, message, throwable);
    }
    
    /**
     * Logs an exception with custom message.
     * @param context the context where the exception occurred
     * @param message custom message
     * @param throwable the exception
     */
    public static void exception(String context, String message, Throwable throwable) {
        log(IStatus.ERROR, formatMessage(context, message), throwable);
    }
    
    /**
     * Logs performance information.
     * @param operation the operation being measured
     * @param durationMs the duration in milliseconds
     */
    public static void performance(String operation, long durationMs) {
        if (isDebugEnabled()) {
            log(IStatus.INFO, "[PERF] " + operation + " took " + durationMs + "ms", null);
        }
    }
    
    /**
     * Logs API request information.
     * @param provider the AI provider
     * @param endpoint the API endpoint
     * @param responseTimeMs the response time
     * @param success whether the request was successful
     */
    public static void apiRequest(String provider, String endpoint, long responseTimeMs, boolean success) {
        String status = success ? "SUCCESS" : "FAILED";
        String message = String.format("[API] %s %s - %s (%dms)", provider, endpoint, status, responseTimeMs);
        
        if (success) {
            debug(message);
        } else {
            warning(message);
        }
    }
    
    /**
     * Checks if debug logging is enabled.
     * @return true if debug logging is enabled
     */
    private static boolean isDebugEnabled() {
        // Check if the plugin is in debug mode
        try {
            return Boolean.parseBoolean(System.getProperty("com.fartech.aiagent.debug", "false"));
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Formats a message with context.
     * @param context the context
     * @param message the message
     * @return the formatted message
     */
    private static String formatMessage(String context, String message) {
        if (context == null || context.trim().isEmpty()) {
            return message;
        }
        return "[" + context + "] " + message;
    }
    
    /**
     * Logs a message with the specified severity.
     * @param severity the severity level
     * @param message the message
     * @param throwable optional throwable
     */
    private static void log(int severity, String message, Throwable throwable) {
        try {
            Status status = new Status(severity, PLUGIN_ID, message, throwable);
            
            // Log to Eclipse error log
            if (Activator.getDefault() != null) {
                Activator.getDefault().getLog().log(status);
            }
            
            // Also log to console in debug mode
            if (isDebugEnabled()) {
                String severityStr = getSeverityString(severity);
                System.out.println("[" + severityStr + "] " + PLUGIN_ID + ": " + message);
                if (throwable != null) {
                    throwable.printStackTrace();
                }
            }
            
        } catch (Exception e) {
            // Fallback to console if logging fails
            System.err.println("Failed to log message: " + message);
            if (throwable != null) {
                throwable.printStackTrace();
            }
            e.printStackTrace();
        }
    }
    
    /**
     * Gets the string representation of a severity level.
     * @param severity the severity level
     * @return the string representation
     */
    private static String getSeverityString(int severity) {
        switch (severity) {
            case IStatus.INFO:
                return "INFO";
            case IStatus.WARNING:
                return "WARN";
            case IStatus.ERROR:
                return "ERROR";
            default:
                return "UNKNOWN";
        }
    }
}
