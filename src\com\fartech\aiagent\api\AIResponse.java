package com.fartech.aiagent.api;

import java.util.Map;
import java.util.HashMap;

/**
 * Represents a response from an AI provider.
 */
public class AIResponse {
    
    private String content;
    private String model;
    private int tokensUsed;
    private long responseTimeMs;
    private boolean success;
    private String errorMessage;
    private Map<String, Object> metadata;
    
    public AIResponse() {
        this.metadata = new HashMap<>();
        this.success = true;
    }
    
    public AIResponse(String content) {
        this();
        this.content = content;
    }
    
    public static AIResponse error(String errorMessage) {
        AIResponse response = new AIResponse();
        response.success = false;
        response.errorMessage = errorMessage;
        return response;
    }
    
    // Getters and setters
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public int getTokensUsed() {
        return tokensUsed;
    }
    
    public void setTokensUsed(int tokensUsed) {
        this.tokensUsed = tokensUsed;
    }
    
    public long getResponseTimeMs() {
        return responseTimeMs;
    }
    
    public void setResponseTimeMs(long responseTimeMs) {
        this.responseTimeMs = responseTimeMs;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.success = false;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(String key, Object value) {
        this.metadata.put(key, value);
    }
    
    public Object getMetadata(String key) {
        return this.metadata.get(key);
    }
}
