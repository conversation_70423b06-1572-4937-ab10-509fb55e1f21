package com.fartech.aiagent.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.handlers.HandlerUtil;

/**
 * <PERSON><PERSON> for the "Open Preferences" command.
 */
public class OpenPreferencesHandler extends AbstractHandler {
    
    @Override
    public Object execute(ExecutionEvent event) throws ExecutionException {
        IWorkbenchWindow window = HandlerUtil.getActiveWorkbenchWindowChecked(event);
        
        try {
            // Open the AI Agent preferences page
            org.eclipse.ui.dialogs.PreferencesUtil.createPreferenceDialogOn(
                window.getShell(), 
                "com.fartech.aiagent.preferences.AIAgentPreferencePage", 
                null, 
                null
            ).open();
            
        } catch (Exception e) {
            MessageDialog.openError(window.getShell(), "Error", 
                "Failed to open preferences: " + e.getMessage());
        }
        
        return null;
    }
}
