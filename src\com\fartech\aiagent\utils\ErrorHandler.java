package com.fartech.aiagent.utils;

import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.widgets.Shell;

import com.fartech.aiagent.api.AIProviderException;

/**
 * Utility class for handling errors in the AI Agent plugin.
 */
public class E<PERSON><PERSON><PERSON><PERSON><PERSON> {
    
    /**
     * Handles an exception by logging it and optionally showing a user dialog.
     * @param context the context where the error occurred
     * @param exception the exception
     * @param shell the shell for showing dialogs (can be null)
     * @param showDialog whether to show a dialog to the user
     */
    public static void handleException(String context, Exception exception, Shell shell, boolean showDialog) {
        // Log the exception
        Logger.exception(context, exception);
        
        // Show dialog if requested and shell is available
        if (showDialog && shell != null && !shell.isDisposed()) {
            String title = "Error";
            String message = getUserFriendlyMessage(exception);
            
            shell.getDisplay().asyncExec(() -> {
                if (!shell.isDisposed()) {
                    MessageDialog.openError(shell, title, message);
                }
            });
        }
    }
    
    /**
     * Handles an exception by logging it and showing a user dialog.
     * @param context the context where the error occurred
     * @param exception the exception
     * @param shell the shell for showing dialogs
     */
    public static void handleException(String context, Exception exception, Shell shell) {
        handleException(context, exception, shell, true);
    }
    
    /**
     * Handles an exception by logging it only (no user dialog).
     * @param context the context where the error occurred
     * @param exception the exception
     */
    public static void handleException(String context, Exception exception) {
        handleException(context, exception, null, false);
    }
    
    /**
     * Handles an AI provider exception with specific messaging.
     * @param context the context where the error occurred
     * @param exception the AI provider exception
     * @param shell the shell for showing dialogs
     */
    public static void handleAIProviderException(String context, AIProviderException exception, Shell shell) {
        Logger.exception(context, exception);
        
        if (shell != null && !shell.isDisposed()) {
            String title = "AI Provider Error";
            String message = getAIProviderErrorMessage(exception);
            
            shell.getDisplay().asyncExec(() -> {
                if (!shell.isDisposed()) {
                    MessageDialog.openError(shell, title, message);
                }
            });
        }
    }
    
    /**
     * Handles configuration errors.
     * @param context the context where the error occurred
     * @param message the error message
     * @param shell the shell for showing dialogs
     */
    public static void handleConfigurationError(String context, String message, Shell shell) {
        Logger.error(context, "Configuration error: " + message);
        
        if (shell != null && !shell.isDisposed()) {
            String title = "Configuration Error";
            String fullMessage = "There is a problem with the AI Agent configuration:\n\n" + message + 
                "\n\nPlease check your settings in the preferences.";
            
            shell.getDisplay().asyncExec(() -> {
                if (!shell.isDisposed()) {
                    MessageDialog.openWarning(shell, title, fullMessage);
                }
            });
        }
    }
    
    /**
     * Handles network/connectivity errors.
     * @param context the context where the error occurred
     * @param exception the exception
     * @param shell the shell for showing dialogs
     */
    public static void handleNetworkError(String context, Exception exception, Shell shell) {
        Logger.exception(context, "Network error", exception);
        
        if (shell != null && !shell.isDisposed()) {
            String title = "Network Error";
            String message = "Failed to connect to the AI provider. Please check:\n\n" +
                "• Your internet connection\n" +
                "• The AI provider's service status\n" +
                "• Your API key and configuration\n" +
                "• Firewall or proxy settings\n\n" +
                "Error details: " + exception.getMessage();
            
            shell.getDisplay().asyncExec(() -> {
                if (!shell.isDisposed()) {
                    MessageDialog.openError(shell, title, message);
                }
            });
        }
    }
    
    /**
     * Shows a warning message to the user.
     * @param context the context
     * @param message the warning message
     * @param shell the shell for showing dialogs
     */
    public static void showWarning(String context, String message, Shell shell) {
        Logger.warning(context, message);
        
        if (shell != null && !shell.isDisposed()) {
            shell.getDisplay().asyncExec(() -> {
                if (!shell.isDisposed()) {
                    MessageDialog.openWarning(shell, "Warning", message);
                }
            });
        }
    }
    
    /**
     * Shows an information message to the user.
     * @param context the context
     * @param message the information message
     * @param shell the shell for showing dialogs
     */
    public static void showInfo(String context, String message, Shell shell) {
        Logger.info(context, message);
        
        if (shell != null && !shell.isDisposed()) {
            shell.getDisplay().asyncExec(() -> {
                if (!shell.isDisposed()) {
                    MessageDialog.openInformation(shell, "Information", message);
                }
            });
        }
    }
    
    /**
     * Converts an exception to a user-friendly message.
     * @param exception the exception
     * @return user-friendly message
     */
    private static String getUserFriendlyMessage(Exception exception) {
        if (exception instanceof AIProviderException) {
            return getAIProviderErrorMessage((AIProviderException) exception);
        }
        
        String message = exception.getMessage();
        if (message == null || message.trim().isEmpty()) {
            message = exception.getClass().getSimpleName();
        }
        
        // Handle common error types
        if (isNetworkError(exception)) {
            return "Network connection failed. Please check your internet connection and try again.";
        }
        
        if (isAuthenticationError(exception)) {
            return "Authentication failed. Please check your API key in the preferences.";
        }
        
        if (isTimeoutError(exception)) {
            return "Request timed out. The AI provider may be experiencing high load. Please try again.";
        }
        
        // Return the original message for other errors
        return "An error occurred: " + message;
    }
    
    /**
     * Gets a user-friendly message for AI provider exceptions.
     * @param exception the AI provider exception
     * @return user-friendly message
     */
    private static String getAIProviderErrorMessage(AIProviderException exception) {
        String providerName = exception.getProviderName();
        String message = exception.getMessage();
        int errorCode = exception.getErrorCode();
        
        StringBuilder sb = new StringBuilder();
        
        if (providerName != null) {
            sb.append("Error with ").append(providerName).append(" provider");
        } else {
            sb.append("AI provider error");
        }
        
        if (errorCode != 0) {
            sb.append(" (").append(errorCode).append(")");
        }
        
        sb.append(":\n\n").append(message);
        
        // Add specific guidance based on error code
        if (errorCode == 401 || message.toLowerCase().contains("unauthorized")) {
            sb.append("\n\nThis usually means your API key is invalid or expired. Please check your configuration.");
        } else if (errorCode == 429 || message.toLowerCase().contains("rate limit")) {
            sb.append("\n\nYou have exceeded the rate limit. Please wait a moment before trying again.");
        } else if (errorCode >= 500) {
            sb.append("\n\nThis appears to be a server error. Please try again later.");
        }
        
        return sb.toString();
    }
    
    /**
     * Checks if an exception is a network-related error.
     * @param exception the exception
     * @return true if it's a network error
     */
    private static boolean isNetworkError(Exception exception) {
        String message = exception.getMessage();
        if (message == null) {
            return false;
        }
        
        message = message.toLowerCase();
        return message.contains("connection") || 
               message.contains("network") || 
               message.contains("host") ||
               message.contains("socket") ||
               exception instanceof java.net.ConnectException ||
               exception instanceof java.net.UnknownHostException;
    }
    
    /**
     * Checks if an exception is an authentication error.
     * @param exception the exception
     * @return true if it's an authentication error
     */
    private static boolean isAuthenticationError(Exception exception) {
        String message = exception.getMessage();
        if (message == null) {
            return false;
        }
        
        message = message.toLowerCase();
        return message.contains("unauthorized") || 
               message.contains("authentication") || 
               message.contains("api key") ||
               message.contains("401");
    }
    
    /**
     * Checks if an exception is a timeout error.
     * @param exception the exception
     * @return true if it's a timeout error
     */
    private static boolean isTimeoutError(Exception exception) {
        String message = exception.getMessage();
        if (message == null) {
            return false;
        }
        
        message = message.toLowerCase();
        return message.contains("timeout") || 
               message.contains("timed out") ||
               exception instanceof java.net.SocketTimeoutException;
    }
}
