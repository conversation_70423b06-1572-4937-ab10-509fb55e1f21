package com.fartech.aiagent.providers;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

import com.fartech.aiagent.api.AIProvider;
import com.fartech.aiagent.api.AIRequest;
import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.api.AIProviderException;
import com.fartech.aiagent.services.HttpClientService;

/**
 * Custom AI provider implementation that allows users to configure their own AI service.
 */
public class CustomProvider implements AIProvider {
    
    private static final String PROVIDER_ID = "custom";
    private static final String DISPLAY_NAME = "Custom Provider";
    private static final String DESCRIPTION = "Custom AI provider with configurable endpoint and models";
    
    // Configuration keys
    private static final String API_KEY = "api_key";
    private static final String BASE_URL = "base_url";
    private static final String MODEL = "model";
    private static final String AVAILABLE_MODELS = "available_models";
    private static final String API_FORMAT = "api_format";
    private static final String AUTH_HEADER = "auth_header";
    private static final String ENDPOINT_PATH = "endpoint_path";
    
    private Map<String, String> configuration;
    private HttpClientService httpClient;
    private List<String> availableModels;
    
    public CustomProvider() {
        this.configuration = new HashMap<>();
        this.httpClient = new HttpClientService();
        this.availableModels = new ArrayList<>();
    }
    
    @Override
    public String getProviderId() {
        return PROVIDER_ID;
    }
    
    @Override
    public String getDisplayName() {
        return DISPLAY_NAME;
    }
    
    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
    
    @Override
    public void configure(Map<String, String> configuration) throws AIProviderException {
        if (configuration == null) {
            throw new AIProviderException(PROVIDER_ID, "Configuration cannot be null");
        }
        
        String baseUrl = configuration.get(BASE_URL);
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new AIProviderException(PROVIDER_ID, "Base URL is required");
        }
        
        String apiKey = configuration.get(API_KEY);
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new AIProviderException(PROVIDER_ID, "API key is required");
        }
        
        this.configuration.clear();
        this.configuration.putAll(configuration);
        
        // Set default values
        if (!this.configuration.containsKey(API_FORMAT)) {
            this.configuration.put(API_FORMAT, "openai"); // Default to OpenAI format
        }
        if (!this.configuration.containsKey(AUTH_HEADER)) {
            this.configuration.put(AUTH_HEADER, "Authorization");
        }
        if (!this.configuration.containsKey(ENDPOINT_PATH)) {
            this.configuration.put(ENDPOINT_PATH, "/v1/chat/completions");
        }
        
        // Parse available models
        parseAvailableModels();
    }
    
    @Override
    public boolean isConfigured() {
        return configuration.containsKey(API_KEY) && 
               !configuration.get(API_KEY).trim().isEmpty() &&
               configuration.containsKey(BASE_URL) && 
               !configuration.get(BASE_URL).trim().isEmpty();
    }
    
    @Override
    public boolean validateConfiguration() throws AIProviderException {
        if (!isConfigured()) {
            throw new AIProviderException(PROVIDER_ID, "Provider is not configured");
        }
        
        try {
            // Test the configuration by making a simple API call
            AIRequest testRequest = new AIRequest("Hello");
            testRequest.setMaxTokens(5);
            AIResponse response = sendRequest(testRequest);
            return response.isSuccess();
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Configuration validation failed", e);
        }
    }
    
    @Override
    public AIResponse sendRequest(AIRequest request) throws AIProviderException {
        if (!isConfigured()) {
            throw new AIProviderException(PROVIDER_ID, "Provider is not configured");
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            String apiFormat = configuration.get(API_FORMAT);
            Map<String, Object> payload;
            
            // Build request payload based on API format
            if ("anthropic".equals(apiFormat)) {
                payload = buildAnthropicPayload(request);
            } else {
                payload = buildOpenAIPayload(request); // Default to OpenAI format
            }
            
            // Set headers
            Map<String, String> headers = buildHeaders();
            
            // Make the HTTP request
            String url = buildRequestUrl();
            String responseBody = httpClient.post(url, payload, headers);
            
            // Parse the response based on API format
            AIResponse response;
            if ("anthropic".equals(apiFormat)) {
                response = parseAnthropicResponse(responseBody);
            } else {
                response = parseOpenAIResponse(responseBody);
            }
            
            response.setResponseTimeMs(System.currentTimeMillis() - startTime);
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Request failed", e);
        }
    }
    
    @Override
    public String[] getRequiredConfigurationKeys() {
        return new String[] { API_KEY, BASE_URL };
    }
    
    @Override
    public String[] getOptionalConfigurationKeys() {
        return new String[] { MODEL, AVAILABLE_MODELS, API_FORMAT, AUTH_HEADER, ENDPOINT_PATH };
    }
    
    @Override
    public String getConfigurationHelp(String key) {
        switch (key) {
            case API_KEY:
                return "Your API key for the custom AI provider";
            case BASE_URL:
                return "Base URL of your AI provider (e.g., https://api.yourprovider.com)";
            case MODEL:
                return "Default model to use (will be selected from available models)";
            case AVAILABLE_MODELS:
                return "Comma-separated list of available models (e.g., model1,model2,model3)";
            case API_FORMAT:
                return "API format: 'openai' for OpenAI-compatible or 'anthropic' for Anthropic-compatible";
            case AUTH_HEADER:
                return "Authorization header name (default: Authorization)";
            case ENDPOINT_PATH:
                return "API endpoint path (default: /v1/chat/completions)";
            default:
                return "No help available for this configuration key";
        }
    }
    
    /**
     * Gets the list of available models for this provider.
     * @return list of available models
     */
    public List<String> getAvailableModels() {
        return new ArrayList<>(availableModels);
    }
    
    /**
     * Checks if a model is available.
     * @param model the model name
     * @return true if the model is available
     */
    public boolean isModelAvailable(String model) {
        return availableModels.contains(model);
    }
    
    /**
     * Parses the available models from configuration.
     */
    private void parseAvailableModels() {
        availableModels.clear();
        String modelsConfig = configuration.get(AVAILABLE_MODELS);
        
        if (modelsConfig != null && !modelsConfig.trim().isEmpty()) {
            String[] models = modelsConfig.split(",");
            for (String model : models) {
                String trimmed = model.trim();
                if (!trimmed.isEmpty()) {
                    availableModels.add(trimmed);
                }
            }
        }
        
        // If no models configured, add a default
        if (availableModels.isEmpty()) {
            availableModels.add("default-model");
        }
    }
    
    /**
     * Builds the request URL.
     * @return the complete request URL
     */
    private String buildRequestUrl() {
        String baseUrl = configuration.get(BASE_URL);
        String endpointPath = configuration.get(ENDPOINT_PATH);
        
        // Remove trailing slash from base URL
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        
        // Ensure endpoint path starts with /
        if (!endpointPath.startsWith("/")) {
            endpointPath = "/" + endpointPath;
        }
        
        return baseUrl + endpointPath;
    }
    
    /**
     * Builds the request headers.
     * @return the headers map
     */
    private Map<String, String> buildHeaders() {
        Map<String, String> headers = new HashMap<>();
        
        String authHeader = configuration.get(AUTH_HEADER);
        String apiKey = configuration.get(API_KEY);
        String apiFormat = configuration.get(API_FORMAT);
        
        // Set authorization header
        if ("anthropic".equals(apiFormat)) {
            headers.put("x-api-key", apiKey);
            headers.put("anthropic-version", "2023-06-01");
        } else {
            // Default to Bearer token format
            headers.put(authHeader, "Bearer " + apiKey);
        }
        
        headers.put("Content-Type", "application/json");
        
        return headers;
    }
    
    /**
     * Builds OpenAI-compatible request payload.
     * @param request the AI request
     * @return the payload map
     */
    private Map<String, Object> buildOpenAIPayload(AIRequest request) {
        Map<String, Object> payload = new HashMap<>();
        
        // Set model
        String model = request.getModel();
        if (model == null) {
            model = configuration.get(MODEL);
        }
        if (model == null && !availableModels.isEmpty()) {
            model = availableModels.get(0); // Use first available model
        }
        payload.put("model", model);
        
        // Build messages array
        Object[] messages;
        if (request.getSystemMessage() != null) {
            messages = new Object[] {
                createMessage("system", request.getSystemMessage()),
                createMessage("user", request.getPrompt())
            };
        } else {
            messages = new Object[] {
                createMessage("user", request.getPrompt())
            };
        }
        payload.put("messages", messages);
        
        // Set parameters
        payload.put("temperature", request.getTemperature());
        payload.put("max_tokens", request.getMaxTokens());
        
        return payload;
    }
    
    /**
     * Builds Anthropic-compatible request payload.
     * @param request the AI request
     * @return the payload map
     */
    private Map<String, Object> buildAnthropicPayload(AIRequest request) {
        Map<String, Object> payload = new HashMap<>();
        
        // Set model
        String model = request.getModel();
        if (model == null) {
            model = configuration.get(MODEL);
        }
        if (model == null && !availableModels.isEmpty()) {
            model = availableModels.get(0); // Use first available model
        }
        payload.put("model", model);
        
        // Build messages array
        Object[] messages;
        if (request.getSystemMessage() != null) {
            payload.put("system", request.getSystemMessage());
            messages = new Object[] {
                createMessage("user", request.getPrompt())
            };
        } else {
            messages = new Object[] {
                createMessage("user", request.getPrompt())
            };
        }
        payload.put("messages", messages);
        
        // Set parameters
        payload.put("max_tokens", request.getMaxTokens());
        
        return payload;
    }
    
    /**
     * Creates a message object.
     * @param role the message role
     * @param content the message content
     * @return the message map
     */
    private Map<String, String> createMessage(String role, String content) {
        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        return message;
    }
    
    /**
     * Parses OpenAI-compatible response.
     * @param responseBody the response body
     * @return the AI response
     * @throws AIProviderException if parsing fails
     */
    private AIResponse parseOpenAIResponse(String responseBody) throws AIProviderException {
        try {
            AIResponse response = new AIResponse();
            
            // Extract content from the response
            if (responseBody.contains("\"content\"")) {
                int contentStart = responseBody.indexOf("\"content\":\"") + 11;
                int contentEnd = responseBody.indexOf("\"", contentStart);
                if (contentEnd > contentStart) {
                    String content = responseBody.substring(contentStart, contentEnd);
                    content = content.replace("\\n", "\n").replace("\\\"", "\"");
                    response.setContent(content);
                }
            }
            
            // Extract usage information if available
            if (responseBody.contains("\"total_tokens\"")) {
                int tokensStart = responseBody.indexOf("\"total_tokens\":") + 15;
                int tokensEnd = responseBody.indexOf(",", tokensStart);
                if (tokensEnd == -1) tokensEnd = responseBody.indexOf("}", tokensStart);
                if (tokensEnd > tokensStart) {
                    try {
                        int tokens = Integer.parseInt(responseBody.substring(tokensStart, tokensEnd).trim());
                        response.setTokensUsed(tokens);
                    } catch (NumberFormatException e) {
                        // Ignore parsing errors
                    }
                }
            }
            
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Failed to parse response", e);
        }
    }
    
    /**
     * Parses Anthropic-compatible response.
     * @param responseBody the response body
     * @return the AI response
     * @throws AIProviderException if parsing fails
     */
    private AIResponse parseAnthropicResponse(String responseBody) throws AIProviderException {
        try {
            AIResponse response = new AIResponse();
            
            // Extract content from the response
            if (responseBody.contains("\"text\"")) {
                int contentStart = responseBody.indexOf("\"text\":\"") + 8;
                int contentEnd = responseBody.indexOf("\"", contentStart);
                if (contentEnd > contentStart) {
                    String content = responseBody.substring(contentStart, contentEnd);
                    content = content.replace("\\n", "\n").replace("\\\"", "\"");
                    response.setContent(content);
                }
            }
            
            // Extract usage information if available
            if (responseBody.contains("\"input_tokens\"")) {
                int inputStart = responseBody.indexOf("\"input_tokens\":") + 15;
                int inputEnd = responseBody.indexOf(",", inputStart);
                if (inputEnd == -1) inputEnd = responseBody.indexOf("}", inputStart);
                
                int outputStart = responseBody.indexOf("\"output_tokens\":") + 16;
                int outputEnd = responseBody.indexOf(",", outputStart);
                if (outputEnd == -1) outputEnd = responseBody.indexOf("}", outputStart);
                
                try {
                    int inputTokens = Integer.parseInt(responseBody.substring(inputStart, inputEnd).trim());
                    int outputTokens = Integer.parseInt(responseBody.substring(outputStart, outputEnd).trim());
                    response.setTokensUsed(inputTokens + outputTokens);
                } catch (NumberFormatException e) {
                    // Ignore parsing errors
                }
            }
            
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Failed to parse response", e);
        }
    }
}
