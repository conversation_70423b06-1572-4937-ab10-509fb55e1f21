package com.fartech.aiagent.providers;

import java.util.Map;
import java.util.HashMap;

import com.fartech.aiagent.api.AIProvider;
import com.fartech.aiagent.api.AIRequest;
import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.api.AIProviderException;
import com.fartech.aiagent.services.HttpClientService;

/**
 * OpenAI provider implementation for GPT models.
 */
public class OpenAIProvider implements AIProvider {
    
    private static final String PROVIDER_ID = "openai";
    private static final String DISPLAY_NAME = "OpenAI";
    private static final String DESCRIPTION = "OpenAI GPT models (GPT-3.5, GPT-4, etc.)";
    
    private static final String API_KEY = "api_key";
    private static final String BASE_URL = "base_url";
    private static final String MODEL = "model";
    private static final String ORGANIZATION = "organization";
    
    private Map<String, String> configuration;
    private HttpClientService httpClient;
    
    public OpenAIProvider() {
        this.configuration = new HashMap<>();
        this.httpClient = new HttpClientService();
    }
    
    @Override
    public String getProviderId() {
        return PROVIDER_ID;
    }
    
    @Override
    public String getDisplayName() {
        return DISPLAY_NAME;
    }
    
    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
    
    @Override
    public void configure(Map<String, String> configuration) throws AIProviderException {
        if (configuration == null) {
            throw new AIProviderException(PROVIDER_ID, "Configuration cannot be null");
        }
        
        String apiKey = configuration.get(API_KEY);
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new AIProviderException(PROVIDER_ID, "API key is required");
        }
        
        this.configuration.clear();
        this.configuration.putAll(configuration);
        
        // Set default values
        if (!this.configuration.containsKey(BASE_URL)) {
            this.configuration.put(BASE_URL, "https://api.openai.com/v1");
        }
        if (!this.configuration.containsKey(MODEL)) {
            this.configuration.put(MODEL, "gpt-3.5-turbo");
        }
    }
    
    @Override
    public boolean isConfigured() {
        return configuration.containsKey(API_KEY) && 
               !configuration.get(API_KEY).trim().isEmpty();
    }
    
    @Override
    public boolean validateConfiguration() throws AIProviderException {
        if (!isConfigured()) {
            throw new AIProviderException(PROVIDER_ID, "Provider is not configured");
        }
        
        try {
            // Test the configuration by making a simple API call
            AIRequest testRequest = new AIRequest("Hello");
            testRequest.setMaxTokens(5);
            AIResponse response = sendRequest(testRequest);
            return response.isSuccess();
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Configuration validation failed", e);
        }
    }
    
    @Override
    public AIResponse sendRequest(AIRequest request) throws AIProviderException {
        if (!isConfigured()) {
            throw new AIProviderException(PROVIDER_ID, "Provider is not configured");
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // Build the request payload
            Map<String, Object> payload = buildRequestPayload(request);
            
            // Set headers
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + configuration.get(API_KEY));
            headers.put("Content-Type", "application/json");
            
            if (configuration.containsKey(ORGANIZATION)) {
                headers.put("OpenAI-Organization", configuration.get(ORGANIZATION));
            }
            
            // Make the HTTP request
            String url = configuration.get(BASE_URL) + "/chat/completions";
            String responseBody = httpClient.post(url, payload, headers);
            
            // Parse the response
            AIResponse response = parseResponse(responseBody);
            response.setResponseTimeMs(System.currentTimeMillis() - startTime);
            
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Request failed", e);
        }
    }
    
    @Override
    public String[] getRequiredConfigurationKeys() {
        return new String[] { API_KEY };
    }
    
    @Override
    public String[] getOptionalConfigurationKeys() {
        return new String[] { BASE_URL, MODEL, ORGANIZATION };
    }
    
    @Override
    public String getConfigurationHelp(String key) {
        switch (key) {
            case API_KEY:
                return "Your OpenAI API key. Get it from https://platform.openai.com/api-keys";
            case BASE_URL:
                return "OpenAI API base URL (default: https://api.openai.com/v1)";
            case MODEL:
                return "Model to use (default: gpt-3.5-turbo). Options: gpt-3.5-turbo, gpt-4, gpt-4-turbo-preview";
            case ORGANIZATION:
                return "Optional organization ID for OpenAI API requests";
            default:
                return "No help available for this configuration key";
        }
    }
    
    private Map<String, Object> buildRequestPayload(AIRequest request) {
        Map<String, Object> payload = new HashMap<>();
        
        // Set model
        String model = request.getModel();
        if (model == null) {
            model = configuration.get(MODEL);
        }
        payload.put("model", model);
        
        // Build messages array
        Object[] messages;
        if (request.getSystemMessage() != null) {
            messages = new Object[] {
                createMessage("system", request.getSystemMessage()),
                createMessage("user", request.getPrompt())
            };
        } else {
            messages = new Object[] {
                createMessage("user", request.getPrompt())
            };
        }
        payload.put("messages", messages);
        
        // Set parameters
        payload.put("temperature", request.getTemperature());
        payload.put("max_tokens", request.getMaxTokens());
        
        return payload;
    }
    
    private Map<String, String> createMessage(String role, String content) {
        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        return message;
    }
    
    private AIResponse parseResponse(String responseBody) throws AIProviderException {
        // This is a simplified JSON parsing - in a real implementation,
        // you would use a proper JSON library like Jackson or Gson
        try {
            AIResponse response = new AIResponse();
            
            // Extract content from the response
            // This is a very basic implementation - you should use proper JSON parsing
            if (responseBody.contains("\"content\"")) {
                int contentStart = responseBody.indexOf("\"content\":\"") + 11;
                int contentEnd = responseBody.indexOf("\"", contentStart);
                if (contentEnd > contentStart) {
                    String content = responseBody.substring(contentStart, contentEnd);
                    content = content.replace("\\n", "\n").replace("\\\"", "\"");
                    response.setContent(content);
                }
            }
            
            // Extract usage information if available
            if (responseBody.contains("\"total_tokens\"")) {
                int tokensStart = responseBody.indexOf("\"total_tokens\":") + 15;
                int tokensEnd = responseBody.indexOf(",", tokensStart);
                if (tokensEnd == -1) tokensEnd = responseBody.indexOf("}", tokensStart);
                if (tokensEnd > tokensStart) {
                    try {
                        int tokens = Integer.parseInt(responseBody.substring(tokensStart, tokensEnd).trim());
                        response.setTokensUsed(tokens);
                    } catch (NumberFormatException e) {
                        // Ignore parsing errors
                    }
                }
            }
            
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Failed to parse response", e);
        }
    }
}
