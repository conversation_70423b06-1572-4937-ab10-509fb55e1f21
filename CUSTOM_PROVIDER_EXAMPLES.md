# Custom AI Provider Configuration Examples

This document provides examples of how to configure the Custom Provider for different AI services.

## Configuration Fields

When you select "Custom Provider" in the AI Agent preferences, you'll see these configuration fields:

### Required Fields
- **API Key**: Your API key for the AI service
- **Base URL**: The base URL of your AI provider's API

### Optional Fields
- **Model**: The default model to use (selected from Available Models dropdown)
- **Available Models**: Comma-separated list of models your provider supports
- **API Format**: Choose between "openai" (OpenAI-compatible) or "anthropic" (Anthropic-compatible)
- **Auth Header**: The authorization header name (default: "Authorization")
- **Endpoint Path**: The API endpoint path (default: "/v1/chat/completions")

## Example Configurations

### Example 1: OpenAI-Compatible Service

If you have an OpenAI-compatible API service:

```
API Key: your-api-key-here
Base URL: https://api.yourservice.com
Available Models: gpt-3.5-turbo,gpt-4,your-custom-model-1,your-custom-model-2
API Format: openai
Auth Header: Authorization
Endpoint Path: /v1/chat/completions
Model: gpt-3.5-turbo
```

### Example 2: Anthropic-Compatible Service

If you have an Anthropic-compatible API service:

```
API Key: your-api-key-here
Base URL: https://api.yourservice.com
Available Models: claude-3-opus,claude-3-sonnet,your-custom-claude-model
API Format: anthropic
Auth Header: x-api-key
Endpoint Path: /v1/messages
Model: claude-3-sonnet
```

### Example 3: Local AI Service (like Ollama)

For a local AI service running on your machine:

```
API Key: not-required-but-fill-something
Base URL: http://localhost:11434
Available Models: llama2,codellama,mistral,your-local-model
API Format: openai
Auth Header: Authorization
Endpoint Path: /v1/chat/completions
Model: llama2
```

### Example 4: Azure OpenAI Service

For Azure OpenAI with custom endpoint:

```
API Key: your-azure-api-key
Base URL: https://your-resource.openai.azure.com
Available Models: gpt-35-turbo,gpt-4,gpt-4-32k
API Format: openai
Auth Header: api-key
Endpoint Path: /openai/deployments/your-deployment/chat/completions?api-version=2023-12-01-preview
Model: gpt-35-turbo
```

### Example 5: Hugging Face Inference API

For Hugging Face Inference API:

```
API Key: your-hf-token
Base URL: https://api-inference.huggingface.co
Available Models: microsoft/DialoGPT-large,facebook/blenderbot-400M-distill,your-model
API Format: openai
Auth Header: Authorization
Endpoint Path: /models/microsoft/DialoGPT-large
Model: microsoft/DialoGPT-large
```

## API Format Details

### OpenAI Format
The OpenAI format expects requests like:
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello!"}
  ],
  "temperature": 0.7,
  "max_tokens": 1000
}
```

### Anthropic Format
The Anthropic format expects requests like:
```json
{
  "model": "claude-3-sonnet-20240229",
  "system": "You are a helpful assistant.",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "max_tokens": 1000
}
```

## Authentication Headers

Different services use different authentication methods:

- **OpenAI**: `Authorization: Bearer your-api-key`
- **Anthropic**: `x-api-key: your-api-key`
- **Azure**: `api-key: your-api-key`
- **Custom**: Configure as needed for your service

## Testing Your Configuration

1. Fill in all the required fields in the preferences
2. Add your available models (comma-separated)
3. Select a model from the dropdown
4. Click "Test Connection" to verify your configuration
5. If successful, you'll see "✓ Connection successful!"

## Troubleshooting

### Common Issues

1. **"Connection failed"**
   - Check your Base URL is correct
   - Verify your API key is valid
   - Ensure the Endpoint Path is correct for your service

2. **"Authentication failed"**
   - Verify your API key
   - Check the Auth Header name is correct for your service

3. **"Model not found"**
   - Ensure the model name in your Available Models list is correct
   - Check that the selected model is supported by your service

4. **"Invalid request format"**
   - Try switching between "openai" and "anthropic" API formats
   - Check if your service expects a different request structure

### Debug Tips

1. Enable debug logging by adding `-Dcom.fartech.aiagent.debug=true` to Eclipse startup
2. Check the Eclipse Error Log for detailed error messages
3. Test your API manually with curl or Postman first
4. Start with a simple configuration and add complexity gradually

## Adding New Models

To add new models to your custom provider:

1. Go to AI Agent preferences
2. Select your Custom Provider
3. Update the "Available Models" field with comma-separated model names
4. The Model dropdown will automatically update
5. Select your new model and test the connection

## Security Notes

- API keys are stored securely in Eclipse preferences
- Keys are masked in the UI with asterisks
- Consider using environment variables for sensitive keys in production
- Be cautious when sharing Eclipse workspace files that might contain preferences

## Support

If you encounter issues with your custom provider configuration:

1. Check this examples document
2. Review the Eclipse Error Log
3. Test your API endpoint independently
4. Create an issue in the project repository with your configuration details (without sensitive information)
