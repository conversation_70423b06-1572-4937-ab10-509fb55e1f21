# AI Coding Agent for Eclipse

An Eclipse plugin that integrates AI-powered coding assistance directly into your Eclipse IDE. This plugin supports multiple AI providers and offers intelligent code analysis, generation, and suggestions.

## Features

- **Multiple AI Provider Support**: Configure OpenAI, Anthropic, or other AI providers
- **Code Analysis**: Get intelligent feedback and suggestions for your Java code
- **Code Generation**: Generate code based on natural language descriptions
- **Code Explanation**: Get clear explanations of complex code sections
- **Eclipse Integration**: Seamlessly integrated into Eclipse IDE with views, menus, and commands
- **Secure Configuration**: Safely store API keys and provider settings

## Requirements

- Eclipse 2019-12 or later
- Java 8 or later
- Internet connection for AI provider APIs
- Valid API key for your chosen AI provider

## Installation

### From Source

1. Clone or download this repository
2. Import the project into Eclipse as an existing project
3. Right-click the project and select "Export" > "Plug-in Development" > "Deployable plug-ins and fragments"
4. Install the generated plugin into your Eclipse installation

### Development Setup

1. Open Eclipse with Plugin Development Environment (PDE)
2. Import this project as an existing project
3. Right-click the project and select "Run As" > "Eclipse Application" to test

## Configuration

1. Open Eclipse preferences: **Window** > **Preferences**
2. Navigate to **AI Agent** in the preferences tree
3. Select your preferred AI provider (OpenAI or Anthropic)
4. Configure the required settings:

### OpenAI Configuration
- **API Key**: Your OpenAI API key (get from https://platform.openai.com/api-keys)
- **Base URL**: API base URL (default: https://api.openai.com/v1)
- **Model**: Model to use (default: gpt-3.5-turbo)
- **Organization**: Optional organization ID

### Anthropic Configuration
- **API Key**: Your Anthropic API key (get from https://console.anthropic.com/)
- **Base URL**: API base URL (default: https://api.anthropic.com)
- **Model**: Model to use (default: claude-3-sonnet-20240229)
- **Version**: API version (default: 2023-06-01)

5. Click **Test Connection** to verify your configuration
6. Click **Apply and Close** to save your settings

## Usage

### AI Assistant View

1. Open the AI Assistant view: **Window** > **Show View** > **Other** > **AI Agent** > **AI Assistant**
2. Type your questions or requests in the input area
3. Press **Send** or **Ctrl+Enter** to get AI responses
4. Use the toolbar buttons for quick actions

### Menu Commands

Access AI features through the **AI Agent** menu:

- **Analyze Code**: Analyze selected code in the editor
- **Generate Code**: Generate code based on your description
- **Preferences**: Open AI Agent configuration

### Context Menu Integration

Right-click in any Java editor to access AI features for the selected code.

## Supported AI Providers

### OpenAI
- GPT-3.5 Turbo
- GPT-4
- GPT-4 Turbo

### Anthropic
- Claude 3 Opus
- Claude 3 Sonnet
- Claude 3 Haiku

## Project Structure

```
src/
├── com/fartech/aiagent/
│   ├── api/                    # AI provider interfaces
│   │   ├── AIProvider.java
│   │   ├── AIRequest.java
│   │   ├── AIResponse.java
│   │   └── AIProviderException.java
│   ├── providers/              # AI provider implementations
│   │   ├── OpenAIProvider.java
│   │   ├── AnthropicProvider.java
│   │   └── AIProviderRegistry.java
│   ├── services/               # Core services
│   │   ├── AIAgentService.java
│   │   └── HttpClientService.java
│   ├── preferences/            # Eclipse preferences
│   │   ├── AIAgentPreferencePage.java
│   │   ├── AIAgentPreferences.java
│   │   └── PreferenceInitializer.java
│   ├── views/                  # Eclipse views
│   │   └── AIAgentView.java
│   ├── handlers/               # Command handlers
│   │   ├── AnalyzeCodeHandler.java
│   │   ├── GenerateCodeHandler.java
│   │   └── OpenPreferencesHandler.java
│   ├── utils/                  # Utility classes
│   │   ├── Logger.java
│   │   └── ErrorHandler.java
│   └── Activator.java          # Plugin activator
```

## Development

### Adding New AI Providers

1. Implement the `AIProvider` interface
2. Register the provider in `AIProviderRegistry`
3. Add configuration fields to the preferences page

### Extending Functionality

The plugin is designed to be extensible. You can:

- Add new AI provider implementations
- Create additional views and editors
- Implement new command handlers
- Add custom code analysis features

## Troubleshooting

### Common Issues

1. **"AI Agent Not Configured"**
   - Ensure you have configured an AI provider in preferences
   - Verify your API key is correct and has sufficient credits

2. **"Connection Failed"**
   - Check your internet connection
   - Verify the API endpoint URLs
   - Check if your firewall allows outbound connections

3. **"Authentication Failed"**
   - Verify your API key is correct
   - Check if your API key has the necessary permissions

### Debug Mode

Enable debug logging by adding this JVM argument when starting Eclipse:
```
-Dcom.fartech.aiagent.debug=true
```

### Logs

Check the Eclipse Error Log view for detailed error information:
**Window** > **Show View** > **Other** > **General** > **Error Log**

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the Eclipse Error Log
3. Create an issue in the project repository

## Changelog

### Version 1.0.0
- Initial release
- Support for OpenAI and Anthropic providers
- Basic code analysis and generation features
- Eclipse preferences integration
- AI Assistant view
