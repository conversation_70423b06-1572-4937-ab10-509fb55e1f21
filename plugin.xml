<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <extension
         point="org.eclipse.ui.preferencePages">
      <page
            class="com.fartech.aiagent.preferences.AIAgentPreferencePageEnhanced"
            id="com.fartech.aiagent.preferences.AIAgentPreferencePage"
            name="AI Agent">
      </page>
   </extension>
   
   <extension
         point="org.eclipse.core.runtime.preferences">
      <initializer
            class="com.fartech.aiagent.preferences.PreferenceInitializer">
      </initializer>
   </extension>
   
   <extension
         point="org.eclipse.ui.views">
      <category
            id="com.fartech.aiagent.views"
            name="AI Agent">
      </category>
      <view
            category="com.fartech.aiagent.views"
            class="com.fartech.aiagent.views.AIAgentView"
            id="com.fartech.aiagent.views.AIAgentView"
            name="AI Assistant"
            restorable="true">
      </view>
   </extension>
   
   <extension
         point="org.eclipse.ui.menus">
      <menuContribution
            allPopups="false"
            locationURI="menu:org.eclipse.ui.main.menu">
         <menu
               id="com.fartech.aiagent.menu"
               label="AI Agent"
               mnemonic="A">
            <command
                  commandId="com.fartech.aiagent.commands.analyzeCode"
                  label="Analyze Code"
                  style="push">
            </command>
            <command
                  commandId="com.fartech.aiagent.commands.generateCode"
                  label="Generate Code"
                  style="push">
            </command>
            <separator
                  name="com.fartech.aiagent.separator1"
                  visible="true">
            </separator>
            <command
                  commandId="com.fartech.aiagent.commands.openPreferences"
                  label="Preferences..."
                  style="push">
            </command>
         </menu>
      </menuContribution>
   </extension>
   
   <extension
         point="org.eclipse.ui.commands">
      <command
            defaultHandler="com.fartech.aiagent.handlers.AnalyzeCodeHandler"
            id="com.fartech.aiagent.commands.analyzeCode"
            name="Analyze Code">
      </command>
      <command
            defaultHandler="com.fartech.aiagent.handlers.GenerateCodeHandler"
            id="com.fartech.aiagent.commands.generateCode"
            name="Generate Code">
      </command>
      <command
            defaultHandler="com.fartech.aiagent.handlers.OpenPreferencesHandler"
            id="com.fartech.aiagent.commands.openPreferences"
            name="Open AI Agent Preferences">
      </command>
   </extension>
   
   <extension
         point="org.eclipse.ui.contexts">
      <context
            description="AI Agent Context"
            id="com.fartech.aiagent.context"
            name="AI Agent"
            parentId="org.eclipse.ui.contexts.window">
      </context>
   </extension>
</plugin>
