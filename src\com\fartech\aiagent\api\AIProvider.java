package com.fartech.aiagent.api;

import java.util.Map;

/**
 * Interface for AI providers that can be configured and used by the AI Agent.
 * Implementations should support different AI services like OpenAI, Anthropic, etc.
 */
public interface AIProvider {
    
    /**
     * Gets the unique identifier for this AI provider.
     * @return the provider ID (e.g., "openai", "anthropic", "azure-openai")
     */
    String getProviderId();
    
    /**
     * Gets the display name for this AI provider.
     * @return the human-readable name
     */
    String getDisplayName();
    
    /**
     * Gets the description of this AI provider.
     * @return the provider description
     */
    String getDescription();
    
    /**
     * Configures the provider with the given settings.
     * @param configuration the configuration map containing API keys and other settings
     * @throws AIProviderException if configuration is invalid
     */
    void configure(Map<String, String> configuration) throws AIProviderException;
    
    /**
     * Checks if the provider is properly configured and ready to use.
     * @return true if configured, false otherwise
     */
    boolean isConfigured();
    
    /**
     * Validates the current configuration by testing the connection.
     * @return true if the configuration is valid and connection works
     * @throws AIProviderException if validation fails
     */
    boolean validateConfiguration() throws AIProviderException;
    
    /**
     * Sends a request to the AI provider and returns the response.
     * @param request the AI request containing the prompt and parameters
     * @return the AI response
     * @throws AIProviderException if the request fails
     */
    AIResponse sendRequest(AIRequest request) throws AIProviderException;
    
    /**
     * Gets the required configuration keys for this provider.
     * @return array of required configuration keys
     */
    String[] getRequiredConfigurationKeys();
    
    /**
     * Gets the optional configuration keys for this provider.
     * @return array of optional configuration keys
     */
    String[] getOptionalConfigurationKeys();
    
    /**
     * Gets help text for a specific configuration key.
     * @param key the configuration key
     * @return help text explaining what this key is for
     */
    String getConfigurationHelp(String key);
}
