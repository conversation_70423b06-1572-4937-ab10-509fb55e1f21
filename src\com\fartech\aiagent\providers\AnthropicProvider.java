package com.fartech.aiagent.providers;

import java.util.Map;
import java.util.HashMap;

import com.fartech.aiagent.api.AIProvider;
import com.fartech.aiagent.api.AIRequest;
import com.fartech.aiagent.api.AIResponse;
import com.fartech.aiagent.api.AIProviderException;
import com.fartech.aiagent.services.HttpClientService;

/**
 * Anthropic provider implementation for Claude models.
 */
public class AnthropicProvider implements AIProvider {
    
    private static final String PROVIDER_ID = "anthropic";
    private static final String DISPLAY_NAME = "Anthropic";
    private static final String DESCRIPTION = "Anthropic Claude models (Claude-3, Claude-2, etc.)";
    
    private static final String API_KEY = "api_key";
    private static final String BASE_URL = "base_url";
    private static final String MODEL = "model";
    private static final String VERSION = "version";
    
    private Map<String, String> configuration;
    private HttpClientService httpClient;
    
    public AnthropicProvider() {
        this.configuration = new HashMap<>();
        this.httpClient = new HttpClientService();
    }
    
    @Override
    public String getProviderId() {
        return PROVIDER_ID;
    }
    
    @Override
    public String getDisplayName() {
        return DISPLAY_NAME;
    }
    
    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
    
    @Override
    public void configure(Map<String, String> configuration) throws AIProviderException {
        if (configuration == null) {
            throw new AIProviderException(PROVIDER_ID, "Configuration cannot be null");
        }
        
        String apiKey = configuration.get(API_KEY);
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new AIProviderException(PROVIDER_ID, "API key is required");
        }
        
        this.configuration.clear();
        this.configuration.putAll(configuration);
        
        // Set default values
        if (!this.configuration.containsKey(BASE_URL)) {
            this.configuration.put(BASE_URL, "https://api.anthropic.com");
        }
        if (!this.configuration.containsKey(MODEL)) {
            this.configuration.put(MODEL, "claude-3-sonnet-20240229");
        }
        if (!this.configuration.containsKey(VERSION)) {
            this.configuration.put(VERSION, "2023-06-01");
        }
    }
    
    @Override
    public boolean isConfigured() {
        return configuration.containsKey(API_KEY) && 
               !configuration.get(API_KEY).trim().isEmpty();
    }
    
    @Override
    public boolean validateConfiguration() throws AIProviderException {
        if (!isConfigured()) {
            throw new AIProviderException(PROVIDER_ID, "Provider is not configured");
        }
        
        try {
            // Test the configuration by making a simple API call
            AIRequest testRequest = new AIRequest("Hello");
            testRequest.setMaxTokens(5);
            AIResponse response = sendRequest(testRequest);
            return response.isSuccess();
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Configuration validation failed", e);
        }
    }
    
    @Override
    public AIResponse sendRequest(AIRequest request) throws AIProviderException {
        if (!isConfigured()) {
            throw new AIProviderException(PROVIDER_ID, "Provider is not configured");
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // Build the request payload
            Map<String, Object> payload = buildRequestPayload(request);
            
            // Set headers
            Map<String, String> headers = new HashMap<>();
            headers.put("x-api-key", configuration.get(API_KEY));
            headers.put("Content-Type", "application/json");
            headers.put("anthropic-version", configuration.get(VERSION));
            
            // Make the HTTP request
            String url = configuration.get(BASE_URL) + "/v1/messages";
            String responseBody = httpClient.post(url, payload, headers);
            
            // Parse the response
            AIResponse response = parseResponse(responseBody);
            response.setResponseTimeMs(System.currentTimeMillis() - startTime);
            
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Request failed", e);
        }
    }
    
    @Override
    public String[] getRequiredConfigurationKeys() {
        return new String[] { API_KEY };
    }
    
    @Override
    public String[] getOptionalConfigurationKeys() {
        return new String[] { BASE_URL, MODEL, VERSION };
    }
    
    @Override
    public String getConfigurationHelp(String key) {
        switch (key) {
            case API_KEY:
                return "Your Anthropic API key. Get it from https://console.anthropic.com/";
            case BASE_URL:
                return "Anthropic API base URL (default: https://api.anthropic.com)";
            case MODEL:
                return "Model to use (default: claude-3-sonnet-20240229). Options: claude-3-opus-20240229, claude-3-sonnet-20240229, claude-3-haiku-20240307";
            case VERSION:
                return "API version (default: 2023-06-01)";
            default:
                return "No help available for this configuration key";
        }
    }
    
    private Map<String, Object> buildRequestPayload(AIRequest request) {
        Map<String, Object> payload = new HashMap<>();
        
        // Set model
        String model = request.getModel();
        if (model == null) {
            model = configuration.get(MODEL);
        }
        payload.put("model", model);
        
        // Build messages array
        Object[] messages;
        if (request.getSystemMessage() != null) {
            payload.put("system", request.getSystemMessage());
            messages = new Object[] {
                createMessage("user", request.getPrompt())
            };
        } else {
            messages = new Object[] {
                createMessage("user", request.getPrompt())
            };
        }
        payload.put("messages", messages);
        
        // Set parameters
        payload.put("max_tokens", request.getMaxTokens());
        
        return payload;
    }
    
    private Map<String, String> createMessage(String role, String content) {
        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        return message;
    }
    
    private AIResponse parseResponse(String responseBody) throws AIProviderException {
        // This is a simplified JSON parsing - in a real implementation,
        // you would use a proper JSON library like Jackson or Gson
        try {
            AIResponse response = new AIResponse();
            
            // Extract content from the response
            if (responseBody.contains("\"text\"")) {
                int contentStart = responseBody.indexOf("\"text\":\"") + 8;
                int contentEnd = responseBody.indexOf("\"", contentStart);
                if (contentEnd > contentStart) {
                    String content = responseBody.substring(contentStart, contentEnd);
                    content = content.replace("\\n", "\n").replace("\\\"", "\"");
                    response.setContent(content);
                }
            }
            
            // Extract usage information if available
            if (responseBody.contains("\"input_tokens\"")) {
                int inputStart = responseBody.indexOf("\"input_tokens\":") + 15;
                int inputEnd = responseBody.indexOf(",", inputStart);
                if (inputEnd == -1) inputEnd = responseBody.indexOf("}", inputStart);
                
                int outputStart = responseBody.indexOf("\"output_tokens\":") + 16;
                int outputEnd = responseBody.indexOf(",", outputStart);
                if (outputEnd == -1) outputEnd = responseBody.indexOf("}", outputStart);
                
                try {
                    int inputTokens = Integer.parseInt(responseBody.substring(inputStart, inputEnd).trim());
                    int outputTokens = Integer.parseInt(responseBody.substring(outputStart, outputEnd).trim());
                    response.setTokensUsed(inputTokens + outputTokens);
                } catch (NumberFormatException e) {
                    // Ignore parsing errors
                }
            }
            
            return response;
            
        } catch (Exception e) {
            throw new AIProviderException(PROVIDER_ID, "Failed to parse response", e);
        }
    }
}
