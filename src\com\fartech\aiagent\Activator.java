package com.fartech.aiagent;

import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.BundleContext;

import com.fartech.aiagent.services.AIAgentService;

/**
 * The activator class controls the plug-in life cycle.
 */
public class Activator extends AbstractUIPlugin {

    // The plug-in ID
    public static final String PLUGIN_ID = "com.fartech.aiagent";

    // The shared instance
    private static Activator plugin;

    /**
     * The constructor
     */
    public Activator() {
    }

    /*
     * (non-Javadoc)
     * @see org.eclipse.ui.plugin.AbstractUIPlugin#start(org.osgi.framework.BundleContext)
     */
    public void start(BundleContext context) throws Exception {
        super.start(context);
        plugin = this;
        
        // Initialize the AI Agent Service
        try {
            AIAgentService.getInstance();
            logInfo("AI Agent plugin started successfully");
        } catch (Exception e) {
            logError("Failed to initialize AI Agent service", e);
        }
    }

    /*
     * (non-Javadoc)
     * @see org.eclipse.ui.plugin.AbstractUIPlugin#stop(org.osgi.framework.BundleContext)
     */
    public void stop(BundleContext context) throws Exception {
        plugin = null;
        logInfo("AI Agent plugin stopped");
        super.stop(context);
    }

    /**
     * Returns the shared instance
     *
     * @return the shared instance
     */
    public static Activator getDefault() {
        return plugin;
    }
    
    /**
     * Logs an informational message.
     * @param message the message to log
     */
    public static void logInfo(String message) {
        if (plugin != null) {
            plugin.getLog().log(new org.eclipse.core.runtime.Status(
                org.eclipse.core.runtime.IStatus.INFO, 
                PLUGIN_ID, 
                message
            ));
        }
    }
    
    /**
     * Logs a warning message.
     * @param message the message to log
     */
    public static void logWarning(String message) {
        if (plugin != null) {
            plugin.getLog().log(new org.eclipse.core.runtime.Status(
                org.eclipse.core.runtime.IStatus.WARNING, 
                PLUGIN_ID, 
                message
            ));
        }
    }
    
    /**
     * Logs an error message.
     * @param message the message to log
     * @param throwable optional throwable
     */
    public static void logError(String message, Throwable throwable) {
        if (plugin != null) {
            plugin.getLog().log(new org.eclipse.core.runtime.Status(
                org.eclipse.core.runtime.IStatus.ERROR, 
                PLUGIN_ID, 
                message, 
                throwable
            ));
        }
    }
    
    /**
     * Logs an error message.
     * @param message the message to log
     */
    public static void logError(String message) {
        logError(message, null);
    }
}
